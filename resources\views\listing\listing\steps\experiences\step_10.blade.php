@push('css')
    <style>
        .key_features_step .key_features_fields_wrapper .ck-editor__editable_inline {
            min-height: calc(1.5em * 5);
        }

        .key_features_step .key_features_fields_wrapper .ck .ck-placeholder:before,
        .key_features_step .key_features_fields_wrapper .ck.ck-placeholder:before {
            color: #A9AEC0;
            font-weight: 400
        }
    </style>
@endpush
@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', 'itinerary');
@endphp
<fieldset class="key_features_step watercraft_itenary_step experience_itenary_step" id="">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        {{-- <h2>Tell Guests About Your Itenary</h2> --}}
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="content">
                            <p>{{ $step_data->sub_title ?? "" }}</p>
                        </div>
                    @endisset
                    <div class="key_features_fields_wrapper scrollable-section">
                        <div class="features_add_btn_wrapper justify-content-between">
                            {{-- <label for="">Itenary</label> --}}
                            <label class="ps-0" for="">{{ translate('stepper.select_day') }}</label>
                            <a class="add_feature_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                        </div>

                        <div class="txt_field itinerary_multiple_days_dropdown text-start" style="display: none;">
                            {{-- <label class="ps-0" for="">Select Day</label> --}}
                            <div class="itinerary_days_wrapper">
                                <select class="form-control no_validate" id="itinerary_days" name="itinerary_days">

                                </select>
                            </div>
                        </div>

                        <div class="txt_field">
                            <input class="form-control key_features_title repeater1 no_validate" type="text"
                                placeholder="{{ translate('stepper.name_the_stop_or_activity') }}">
                        </div>
                        <textarea class="ck_editor no_validate" id="itenary_editor"
                            placeholder="{{ translate('stepper.briefly_describe_what_happens_at_this_stop') }}"></textarea>
                        <div class="saved_features_list_wrapper mt-4">
                            @php
                                $itineraryData = $listing?->itineraries?->groupBy('day') ?? [];
                            @endphp
                            @foreach ($itineraryData as $day => $itineraries)
                                <div class="itinerary_day_wrapper" data-itinerary-day="{{ $day }}">
                                    <div class="itinerary_day_title">
                                        <h5>{{ translate('stepper.itinerary') }} {{ translate('stepper.day') }} {{ $day }}</h5>
                                    </div>

                                    @foreach ($itineraries as $itinerary)
                                        <div class="single_saved_feature" data-itinerary-day="{{ $day }}">
                                            <div class="content_cross_wrapper">
                                                <div class="title_description_wrapper w-100">
                                                    <div class="itinerary_start_stop">
                                                        <h6>{{ translate('stepper.itinerary') }}
                                                            @if ($loop->first)
                                                                {{ translate('stepper.start') }}
                                                            @elseif($loop->last)
                                                                {{ translate('stepper.end') }}
                                                            @else
                                                                {{ translate('stepper.stop') }} {{ $loop->index }}
                                                            @endif
                                                        </h6>
                                                    </div>
                                                    <div class="feature_title">
                                                        <h6>{{ $itinerary->title }}</h6>
                                                        <input type="hidden" id="itenary_title_{{ $itinerary->id }}"
                                                            name="itinerary[{{ $day }}][{{ $loop->index }}][title]"
                                                            class="add_input no_validate"
                                                            value="{{ $itinerary->title }}">
                                                    </div>
                                                    <div class="feature_description">
                                                        <p class="fs-14 m-0">
                                                            <input type="hidden" id="itenary_desc_{{ $itinerary->id }}"
                                                                name="itinerary[{{ $day }}][{{ $loop->index }}][description]"
                                                                class="add_input no_validate"
                                                                value="{{ $itinerary->description }}">
                                                            {!! $itinerary->description !!}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="cross_icon_wrapper">
                                                    <a class="remove_key_feature_btn" href="javascript:void(0)">
                                                        <i class="fa fa-trash" aria-hidden="true"
                                                            style="font-size: 16px; color: red;"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        function validateItenaryFields() {
            const inputs = $('.experience_itenary_step .key_features_fields_wrapper .key_features_title');
            const keyFeaturesData = editorInstanceItenary.getData().trim();

            const isAnyFieldFilled = inputs.filter(function() {
                return $(this).val().trim().length > 0;
            }).length > 0;

            $('.experience_itenary_step .next').prop("disabled", (isAnyFieldFilled || keyFeaturesData.length > 0));
        }


        $(document).on('input blur', '.experience_itenary_step .key_features_fields_wrapper .key_features_title',
        function() {
            const itineraryTitle = $('.experience_itenary_step .key_features_fields_wrapper .key_features_title');
            const itineraryDescription = editorInstanceItenary.getData().trim();
            // validateItenaryFields();
            if (itineraryTitle.val().trim().length == 0 && itineraryDescription.length == 0) {
                itineraryToggleNextButton();
            } else {
                validateItenaryFields();
            }
        });

        $(document).on('change', '.experience_itenary_step #itinerary_days', function() {
            const itineraryTitle = $('.experience_itenary_step .key_features_fields_wrapper .key_features_title');
            const itineraryDescription = editorInstanceItenary.getData().trim();
            if (itineraryTitle.val().trim().length == 0 && itineraryDescription.length == 0) {
                itineraryToggleNextButton();
            } else {
                validateItenaryFields();
            }

        });

        function itineraryToggleNextButton() {
            if ($('.experience_itenary_step .saved_features_list_wrapper').children().length === 0) {
                $('.experience_itenary_step .next').prop('disabled', true);
            } else {
                $('.experience_itenary_step .next').prop('disabled', false);
            }
        }


        function observeItineraryChildChanges(element) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // validateItenaryFields();
                        itineraryToggleNextButton();
                    }
                });
            });

            const config = {
                childList: true
            }; // Observe direct children and nested elements (, subtree: true) 
            observer.observe(element, config);
        }

        $('.experience_itenary_step .saved_features_list_wrapper').each(function() {
            observeItineraryChildChanges(this);
        });



        let editorInstanceItenary;
        ClassicEditor
            .create(document.querySelector('#itenary_editor'), {
                enterMode: 'paragraph',
                shiftEnterMode: 'softBreak',
                toolbar: false,
            })
            .then(editor => {
                console.log('Editor was initialized', editor);
                editorInstanceItenary = editor;

                editorInstanceItenary.model.document.on('change:data', () => {
                    if ($('.experience_itenary_step .key_features_fields_wrapper .key_features_title').val()
                        .trim().length == 0 && editorInstanceItenary.getData().trim().length == 0) {
                        itineraryToggleNextButton();
                    } else {
                        validateItenaryFields();
                    }
                });

                editorInstanceItenary.ui.view.editable.element.addEventListener('blur', () => {
                    if ($('.experience_itenary_step .key_features_fields_wrapper .key_features_title').val()
                        .trim().length == 0 && editorInstanceItenary.getData().trim().length == 0) {
                        itineraryToggleNextButton();
                    } else {
                        validateItenaryFields();
                    }

                });

            })
            .catch(error => {
                console.error('There was a problem initializing the editor.', error);
            });


        $(document).ready(function() {
            var counter =
                {{ isset($listing->key_features) ? count($listing->key_features) : 0 }}; // Ensure counter is declared with 'var' to limit its scope
            itineraryToggleNextButton();
            $(document).on('click',
                '.experience_itenary_step .features_add_btn_wrapper .add_feature_btn',
                function() {
                    var title = $(this).closest('fieldset').find('.key_features_title').val().trim();
                    // var description = $(this).closest('fieldset').find('.key_features_description').val().trim();
                    var description = editorInstanceItenary.getData();
                    var plainText = $('<div>').html(description).text();
                    if (window.itinerary_multiple == true) {
                        var itinerarySelectedDays = $('#itinerary_days').find("option:selected").val();
                    } else {
                        var itinerarySelectedDays = 1;
                    }
                    var existingDayItinerary = false;
                    var savedItineraryCount = $('.saved_features_list_wrapper').find(
                        '.single_saved_feature[data-itinerary-day="' + itinerarySelectedDays + '"]').length;
                    console.log(savedItineraryCount);

                    if (savedItineraryCount >= 1) {
                        existingDayItinerary = false;
                    } else {
                        existingDayItinerary = true;
                    }

                    if (title !== "") {
                        if (window.itinerary_multiple == true) {
                            var existingWrapper = $('.saved_features_list_wrapper').find(
                                `.itinerary_day_wrapper[data-itinerary-day="${itinerarySelectedDays}"]`);

                            if (existingWrapper.length === 0) {
                                existingWrapper = $(`<div class="itinerary_day_wrapper" data-itinerary-day="${itinerarySelectedDays}">
                                                        <div class="itinerary_day_title"><h5>{{ translate('stepper.itinerary') }} {{ translate('stepper.day') }} ${itinerarySelectedDays}</h5></div>
                                                    </div>`);
                                $('.experience_itenary_step .saved_features_list_wrapper').append(
                                    existingWrapper);
                            }
                            // Count how many itineraries exist for the selected day
                            var existingFeatures = existingWrapper.find('.single_saved_feature');
                            var itineraryCount = existingFeatures.length;

                            // Determine the heading based on count
                            var headingText = "";
                            if (itineraryCount === 0) {
                                headingText = "{{ translate('stepper.itinerary_start') }}";
                            } else if (itineraryCount === 1) {
                                headingText = "{{ translate('stepper.itinerary_end') }}";
                            } else {
                                headingText = `{{ translate('stepper.itinerary_end') }}`;

                                // Update the last "Itinerary End" to "Itinerary Stop X"
                                existingWrapper.find('.single_saved_feature:last .itinerary_start_stop h6')
                                    .text(`Itinerary Stop ${itineraryCount - 1}`);
                            }

                            // Append the feature inside the existing wrapper
                            var featureHTML = `
                                <div class="single_saved_feature" data-itinerary-day="${itinerarySelectedDays}">
                                    <div class="content_cross_wrapper">
                                        <div class="title_description_wrapper w-100">
                                            <div class="itinerary_start_stop">
                                                <h6>${headingText}</h6>
                                            </div>
                                            <div class="feature_title">
                                                <h6>${title}</h6>
                                                <input type="hidden" id="itenary_title_${counter}" name="itinerary[${itinerarySelectedDays}][${itineraryCount}][title]" class="add_input no_validate" value="${title}" />
                                            </div>
                                            <div class="feature_description">
                                                <p class=" fs-14 m-0">
                                                    <input type="hidden" id="itenary_desc_${counter}" name="itinerary[${itinerarySelectedDays}][${itineraryCount}][description]" class="add_input no_validate" value="${description || '{{ translate('stepper.no_description') }}'}" />
                                                    ${plainText || "{{ translate('stepper.no_description') }}"}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="cross_icon_wrapper">
                                            <a class="remove_key_feature_btn" href="javascript:void(0)">
                                                <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>`;

                            existingWrapper.append(featureHTML); // Append only once inside the correct wrapper

                        } else {
                            // var featureHTML = `
                        //     <div class="single_saved_feature">
                        //         <div class="content_cross_wrapper">
                        //             <div class="title_description_wrapper w-100">
                        //                 <div class="feature_title">
                        //                     <h6>${title}</h6>
                        //                     <input type="hidden" id="itenary_title_${counter}" name="itineraries[${counter}][title]" class="add_input no_validate" value="${title}" />
                        //                 </div>
                        //                 <div class="feature_description">
                        //                     <p class=" fs-14 m-0">
                        //                         <input type="hidden" id="itenary_desc_${counter}" name="itineraries[${counter}][description]" class="add_input no_validate" value="${description || 'No Description'}" />
                        //                         ${plainText || 'No Description'}
                        //                     </p>
                        //                 </div>
                        //             </div>
                        //             <div class="cross_icon_wrapper">
                        //                 <a class="remove_key_feature_btn" href="javascript:void(0)">
                        //                     <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                        //                 </a>
                        //             </div>
                        //         </div>
                        //     </div>`;

                            //     $('.experience_itenary_step .saved_features_list_wrapper').append(
                            // featureHTML);


                            var existingWrapper = $('.saved_features_list_wrapper').find(
                                `.itinerary_day_wrapper[data-itinerary-day="${itinerarySelectedDays}"]`);

                            if (existingWrapper.length === 0) {

                                existingWrapper = $(`<div class="itinerary_day_wrapper" data-itinerary-day="${itinerarySelectedDays}">
                                                    <div class="itinerary_day_title"><h5>{{ translate('stepper.itinerary') }}</h5></div>
                                                </div>`);
                                $('.experience_itenary_step .saved_features_list_wrapper').append(
                                    existingWrapper);
                            }
                            
                            // Count how many itineraries exist for the selected day
                            var existingFeatures = existingWrapper.find('.single_saved_feature');
                            var itineraryCount = existingFeatures.length;

                            // Determine the heading based on count
                            var headingText = "";
                            if (itineraryCount === 0) {
                                headingText = "{{ translate('stepper.itinerary_start') }}";
                            } else if (itineraryCount === 1) {
                                headingText = "{{ translate('stepper.itinerary_end') }}";
                            } else {
                                headingText = `{{ translate('stepper.itinerary_end') }}`;

                                // Update the last "Itinerary End" to "Itinerary Stop X"
                                existingWrapper.find('.single_saved_feature:last .itinerary_start_stop h6')
                                    .text(`Itinerary Stop ${itineraryCount - 1}`);
                            }

                            // Append the feature inside the existing wrapper
                            var featureHTML = `
                                <div class="single_saved_feature" data-itinerary-day="${itinerarySelectedDays}">
                                    <div class="content_cross_wrapper">
                                        <div class="title_description_wrapper w-100">
                                            <div class="itinerary_start_stop">
                                                <h6>${headingText}</h6>
                                            </div>
                                            <div class="feature_title">
                                                <h6>${title}</h6>
                                                <input type="hidden" id="itenary_title_${counter}" name="itinerary[${itinerarySelectedDays}][${itineraryCount}][title]" class="add_input no_validate" value="${title}" />
                                            </div>
                                            <div class="feature_description">
                                                <p class=" fs-14 m-0">
                                                    <input type="hidden" id="itenary_desc_${counter}" name="itinerary[${itinerarySelectedDays}][${itineraryCount}][description]" class="add_input no_validate" value="${description || '{{ translate('stepper.no_description') }}'}" />
                                                    ${plainText || "{{ translate('stepper.no_description') }}"}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="cross_icon_wrapper">
                                            <a class="remove_key_feature_btn" href="javascript:void(0)">
                                                <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>`;

                            existingWrapper.append(featureHTML); // Append only once inside the correct wrapper


                        }

                        // $('.experience_itenary_step .saved_features_list_wrapper').append(
                        //     featureHTML);

                        $(this).closest('fieldset').find('.key_features_title').val('');
                        // $(this).closest('fieldset').find('.key_features_description').val('');
                        if (editorInstanceItenary) {
                            editorInstanceItenary.setData(''); // Set content to an empty string
                        }

                        validateItenaryFields();
                        itineraryToggleNextButton();

                        counter++;
                    } else {
                        Swal.fire({
                            title: @json(translate('stepper.error')),
                            text: @json(translate('stepper.fill_fields_first')),
                            icon: "error"
                        });
                    }
                });

            $(document).on('click', '.experience_itenary_step .remove_key_feature_btn',
                function() {
                    var $singleFeature = $(this).closest('.single_saved_feature');
                    var $wrapper = $singleFeature.closest('.itinerary_day_wrapper');

                    // Remove the clicked feature
                    $singleFeature.remove();

                    // Update itinerary start/stop headings
                    if ($wrapper.length) {
                        var $remainingFeatures = $wrapper.find('.single_saved_feature');

                        if ($remainingFeatures.length > 0) {
                            // Update headings for remaining itinerary items
                            $remainingFeatures.each(function(index) {
                                var headingText = index === 0 ? "Itinerary Start" :
                                    `Itinerary Stop ${index}`;
                                if (index === $remainingFeatures.length - 1) {
                                    headingText = "Itinerary End";
                                }
                                $(this).find('.itinerary_start_stop h6').text(headingText);
                            });
                        } else {
                            // If no items remain, remove the wrapper
                            $wrapper.remove();
                        }
                    }

                    reindexName();
                    itineraryToggleNextButton();

                    if ($('input[name="itineraries[0][title]"]').length > 0) {
                        console.log('Input exists.');
                    } else {
                        $('.itinerary_parent .txt_field input').removeClass('no_validate');
                        $('.itinerary_parent .txt_field textarea').removeClass('no_validate');
                    }
                });

            // function reindexName() {
            //     $('.experience_itenary_step .saved_features_list_wrapper .single_saved_feature')
            //         .each(function(index) {
            //             $(this).find('input').each(function() {
            //                 var name = $(this).attr('name');
            //                 var newName = name.replace(/\[\d+\]/, '[' + index + ']');
            //                 $(this).attr('name', newName);

            //                 var id = $(this).attr('id');
            //                 var baseId = id.substring(0, id.lastIndexOf('_') + 1);
            //                 var newId = baseId + index;
            //                 $(this).attr('id', newId);
            //             });
            //         });
            //     counter = $(
            //         '.experience_itenary_step .saved_features_list_wrapper .single_saved_feature'
            //     ).length;
            // }


            function reindexName() {
                $('.experience_itenary_step .saved_features_list_wrapper .itinerary_day_wrapper').each(function() {
                    var itineraryDay = $(this).find('.single_saved_feature').first().data(
                    'itinerary-day'); // Get the itinerary day

                    $(this).find('.single_saved_feature').each(function(index) {
                        $(this).find('input').each(function() {
                            var name = $(this).attr('name');

                            // Ensure we're updating only the itineraryCount index and not the itinerarySelectedDays
                            var newName = name.replace(/\[\d+\]\[\d+\]/, '[' +
                                itineraryDay + '][' + index + ']');
                            $(this).attr('name', newName);

                            var id = $(this).attr('id');
                            if (id) {
                                var baseId = id.substring(0, id.lastIndexOf('_') + 1);
                                var newId = baseId + index;
                                $(this).attr('id', newId);
                            }
                        });
                    });
                });

                counter = $('.experience_itenary_step .saved_features_list_wrapper .single_saved_feature').length;
            }

            $(".experience_itenary_step .itinerary_multiple_days_dropdown .itinerary_days_wrapper select").on(
                "mousedown",
                function() {
                    $(this).parent().toggleClass("open");
                }).on("blur", function() {
                $(this).parent().removeClass("open");
            });

        });
    </script>
@endpush
