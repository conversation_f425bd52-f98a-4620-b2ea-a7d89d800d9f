/* =============================================================================================================== */
/* global style start from here */

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0;
}

body{ background-color: #f8f9fa;}

:root {
    --primary-color: #FFCE32;
    --secondary-color: #000;
    --text-color-light-grey: #A0AEC0;
    --text-color-dark-grey: #9B9B9B;
    --head-text-color:#4A4A4A;
    --dark-text-color: #2D3748;
    --white-color: #ffffff;
    --black_color:#000000;
    --light-yellow: #FCDF73;
    --comment-grey: #E9E9E9;
    --black: #000;
    --border-grey: #D9D9D9;
    --grey: #E6E6E6;
    --light-grey: #4A4A4A;
}

/* ----------Scrollbar------------------*/

::-webkit-scrollbar {  width: 10px;  background-color: transparent;  }
::-webkit-scrollbar-track {  background-color: transparent;  }
::-webkit-scrollbar-thumb {  background-color: #FCDF73;  border-radius: 20px;  }
::-webkit-scrollbar-thumb:hover {  background-color: #FFCE32;  }

 /* Loader */
 #sec-loader .load { position: fixed; background-color: #fff; z-index: 999999; display: flex; align-items: center; justify-content: center; top: 0; left: 0; height: 100%; width: 100%; margin: auto;}
 #sec-loader .load img { height: 120px;width: 120px;object-fit: contain;}

 .page-wrapper, .navbar-default { background-color: #fff;}

.yellow{ background: var(--primary-color); color: #fff;}
.colorWhite { color: var(--white-color);}
.box-shadow { box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07); }
.b-radius { border-radius: 12px;}
.dark-yellow, .yellow-color { color: var(--primary-color)}

.divider { border-bottom: 1px solid var(--border-grey); }

.navbar-top-links>li>a:hover { background-color: transparent;}
.container-fluid { padding-top: 0;}
.rep_not { padding-block: 30px;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {  -webkit-appearance: none;  margin: 0;  }
.notification .badge { top: 5px; right: 4px;  border-radius: 50%; height: 25px; background-color: #fff; border: 1.5px solid #ffb136;}
.noti_drop .dropdown-alerts li a, .dropdown-tasks li a, .mailbox li>a { padding: 10px 0;}
.noti_drop .dropdown-menu .media .media-heading { padding-bottom: 5px;}
.noti_drop .dropdown-menu .media  { padding-bottom: 0px;}
.noti_drop .dropdown-tasks.dropdown-menu { border-radius: 16px;}
.earning_filter .dropdown-item { font-family: 'Poppins-SemiBold'; }

.ms-1 { margin-left: 10px; }
.ms-2 { margin-left: 20px; }
.ms-3 { margin-left: 30px; }
.ms-4 { margin-left: 40px; }
.ms-5 { margin-left: 50px; }

/* =============================================================================================================== */
/* FONT FAMILY */

@font-face { font-family: 'helvetica-light'; src: url('../website/fonts/helvetica-light.ttf');}
@font-face { font-family: 'Poppins-Medium'; src: url('../website/fonts/Poppins-Medium.ttf');}
@font-face { font-family: 'Poppins-Bold'; src: url('../website/fonts/Poppins-Bold.ttf');}
@font-face { font-family: 'Poppins-SemiBold'; src: url('../website/fonts/Poppins-SemiBold.ttf');}
@font-face { font-family: 'Poppins-Regular'; src: url('../website/fonts/Poppins-Regular.ttf');}

/* =============================================================================================================== */

.sidebar-nav ul#side-menu { margin: 10px; padding: 0;}
.sidebar .sidebar-nav #side-menu li a { color: #fff;}
.sidebar .sidebar-nav ul#side-menu li a.active { color: #fff;}
.sidebar-nav ul#side-menu li.active>a  { color: #ffffff; background: #FFCE32; border-radius: 12px; padding-block: 16px;}
.sidebar-nav ul#side-menu li a i:before { font-size: 15px; background-color: white; padding: 12px 12px; border-radius: 15px; color: #4A4A4A;}
.sidebar-nav ul#side-menu li.active a i:before { background-color: #000; color: #fff;}
.fa-files-lines:before{ content: ''; background-image: url("../plugins/images/clipboard.png"); background-size: auto; display: flex; background-position: center; object-fit: contain; background-repeat: no-repeat;}
.fa-houses { background: white; padding: 5px; border-radius: 12px;}

.position-absolute { position: absolute;}

.waves-effect .badge { top: 8px; right: 6px; height: 25px; width: 25px; padding: 3px;}
.waves-effect .badge p { color: #000;}
.waves-effect.active .badge { background-color: #000; color: #fff;}
.waves-effect.active .badge p { color: #fff;}
.sidebar-nav .waves-effect .badge { padding-left: 5px; top: 11px;}
.mini-sidebar .sidebar-nav li:not(li:hover) .waves-effect .badge { top: -8px; right: 2px;}

.fa-houses:before{ content: ''; background-image: url("../plugins/images/home.png"); background-size: auto; display: flex; background-position: center; object-fit: contain;  background-repeat: no-repeat;}

.fa-file-invoices:before{ content: ''; background-image: url("../plugins/images/calendar.png"); background-size: auto;  display: flex; background-position: center; object-fit: contain; background-repeat: no-repeat;}

.sidebar-nav ul#side-menu li a span { padding-left: 8px; color: white;}
.sidebar-nav ul#side-menu li.active a span { color: #000;}
/* .sidebar-nav ul#side-menu li.active a i:before { background-color: #4A4A4A;color: white; } */

.sidebar-nav ul#side-menu li.dropdown ul { padding-left: 30px; background-color: white;}
.sidebar-nav ul#side-menu li.active a.active.waves-effect span.hide-menu { color: #4A4A4A;}

.rounded { border-radius: 50%;}

/* =============================================================================================================== */
/*HEADINGS PARA SPAN*/

.fa-star-o:before { color: #FFCE32;}
.categories { /* padding-bottom: 15px; */ padding-left: 0; flex-wrap: wrap;}
.catg { background: white; padding: 10px 20px; border: 1px solid lightsteelblue; margin-right: 15px; border-radius: 10px; list-style: none; margin-bottom: 25px; gap: 10px; align-items: center;}

.grey-color{ color: #4A4A4A;}

h1 { font-family: "Poppins-Bold"; font-size: 20px; line-height: 30.8px;}
h2 { font-family: "Poppins-Bold"; font-size: 18px; line-height: 25.2px; color: var(--dark-text-color);}
h3 { font-family: "Poppins-SemiBold"; font-size: 14px; line-height: 19.6px; color: var(--dark-text-color);}
h4 { font-family: "Poppins-Regular"; font-size: 12px; line-height: 18px; color: var(--text-color-light-grey); letter-spacing: 0.5px;}
h5 { font-family: "Poppins-Bold"; font-size: 10px; line-height: 15px; color: var(--text-color-light-grey);}


/* h6 {
    font-family: "SFPRODISPLAYBOLD";
    font-size: 24px;
    line-height: 35px;
    color: var(--primary-color1);
} */

.fs-26 { font-size: 26px; }
.fs-23 { font-size: 23px; font-weight: 600;}
.fs-22 { font-size: 22px;}
.fs-18 { font-size: 18px;}
.fs-16 { font-size: 16px;}
.fs-15 { font-size: 15px;}
.fs-14 { font-size: 14px;}
.fs-13 { font-size: 13px;}
.fs-12 { font-size: 12px;}
.fs-10 { font-size: 10px;}

.semi-bold { font-weight: 600;}
.light-bold { font-weight: 500;}
.regular { font-weight: 400;}

a { list-style: none; text-decoration: none; color: var(--text-color-light-grey); font-family: "Poppins-SemiBold"; font-size: 12px; }
.jodit-container a {all: unset; color: #337ab7}
.jodit-container a:hover {color: #4298e4}
/* .jodit-container a { list-style: unset; text-decoration: "inherit"; color: #337ab7; font-family: "inherit"; font-size: inherit;} */

li { list-style: none; text-decoration: none;}

.inp_text_gap input.form-control { margin-bottom: 1em;}
span.remove_jq i,span.remove_jqu i{ font-size: 21px;}

label { font-family: 'Poppins-medium'; font-size: 16px; font-weight: 500; color: #000; padding-left: 5px; padding-bottom: 6px;}
p { font-family: 'Poppins-Medium'; font-size: 12px; line-height: 18px; color: var(--text-color-dark-grey);}

.tab_fixed .table { table-layout: fixed;}
td a, .text-dark-black { color: var(--dark-text-color);}

/* .listing_table th:nth-child(4), .listing_table td:nth-child(6), .listing_table td:nth-child(9) { text-align: center; } */

footer.footer.t-a-c>div { background: var(--secondary-color); color: white; border-radius: 10px; display: flex; justify-content: space-between;}

.footer a{ color: var(--white-color);}

input.btn.setting_btn { padding-block: 14px; padding-inline: 45px; margin-left: 5px; border-radius: 15px;}
input.btn.setting_btn:hover { background: var(--primary-color); color: var(--white-color);}

.list a.btn.yellow { font-size: 12px; font-weight: 600; width: 100%; height: 38px; border-radius: 12PX; padding-top: 10px;}
.list .form_field_padding { padding-block: 15px; padding-inline: 30px;}

.list .yellow { background: var(--primary-color); color: #000;}
.list .yellow:hover { background: var(--primary-color); color: #fff;}

.cancel_btn{ font-size: 12px; font-weight: 600; font-family: 'poppins-medium'; background: #4A4A4A; padding-block:20px; padding-inline:50px; border-radius: 15px; color: #FFCE32;}
.cancel_btn:hover, .cancel_btn:focus{ background: transparent; border: 1px solid #4A4A4A; color: #FFCE32;}

.list_form_btn { display: flex; gap: 20px;}
.create_btn{ font-size: 12px; font-weight: 600; font-family: 'poppins-medium'; background: #FFCE32; padding-block:20px; padding-inline:50px; border-radius: 15px; color: #4A4A4A;}
.create_btn:hover{ background: transparent; border: 1px solid #FFCE32; color: #4A4A4A;}

.btn_trans, .btn_yellow, .btn_black { font-family: "Poppins-SemiBold";line-height: 15px; border-radius: 12px; padding: 18px 40px; }
.btn_trans  { font-size: 15px; color: var(--head-text-color); border: 1px solid #272727; background-color: transparent; }
.btn_yellow { font-size: 12px; color: var(--head-text-color); border: none; background-color: var(--primary-color); }
.btn_black  { font-size: 15px; color: var(--white-color); border: none; background-color: var(--secondary-color); }

.btn_black:hover { color: var(--secondary-color); border: 2px solid var(--secondary-color); background-color:transparent; padding: 20px 40px;}
.btn_yellow.pull-right { padding: 14px 30px;}
.btn-row .btn_black { padding: 16px 15px; padding-left: 17px; margin-right: 12px; color: white;}
.btn-row .btn_black:hover { color: black;}
.btn-row .btn_yellow{ padding: 2px 7px; padding-left: 17px; margin-right: 12px; color: var(--black_color); display: flex; align-items: center;}
.btn-row .btn-group .fa-filter{ color: var(--black_color);}
section.btn-row { float: right; padding-bottom: 12px;}

.white_color{ color: var(--white-color);}
.black_color, .text-black{ color: var(--black_color);}

.smalWeight { font-weight: 400;}
.semiBold_fontweight{ font-weight: 600;}
.Bold_fontweight{ font-weight: 700; font-size: 37px;}

.wrap-mycard .just-for-padding, .wrap-mycard .just-for-padding2, .wrap-mycard .just-for-padding3 { padding-top: 10px; text-align: left;}
.wrap-mycard .just-for-padding  { padding-right: 13px; }
.wrap-mycard .just-for-padding2 { padding-right: 55px; }
.wrap-mycard .just-for-padding3 { padding-right: 0; }
.btn_trans.topbar {font-size: 12px;}

.invoice_form .new-class{ display: flex; justify-content: end;}
.dropdown-toggle .droppy { padding-left: 20px;}
.new-class .dropdown-toggle{ padding: 15px 15px;}
.s-frm-grp.form-group {    margin-bottom: unset;}
.form-group .selecty { color: #565656a8;}
.form_field_row .spacing{ padding-left: 20px;}
section.table_sect { margin-top: 20px;}


/* =============================================index page css====================================== */
/* nav user */
.dropdown.user-pro-body { display: flex; align-items: end;}

.profile-image img { height: 43px; width: 43px; margin-right: 20px; margin-left: 10px; margin-top: 10px;}
.dropdown li .notification i { padding-top: 23px; }

nav.sidebar-nav { padding-top: 30px;}

/* logout side btn */
.side_btn { padding-left: 30px; padding-block: 10px;}
.side_btn i.fa-solid.fa-right-from-bracket { margin-right: 15px; color: black; background: #FFCE32; font-size: 13px; padding: 9px; border-radius: 12px; transform: rotate(180deg);}
.side_btn a { color: white; font-size: 13px;}

/* logout side btn */
.head_bt .nav-pills:before {    display: none;}

/* table */
table { border-collapse: separate; }
.table_wrraper { background-color: var(--white-color); border-radius: 15px; padding-inline: 20px; padding-block: 10px; box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07);}
.table.dataTable { border-spacing: 0 10px; }
.table.dataTable, .table.dataTable tr { border: none;}
.dataTables_wrapper { overflow-y: visible; padding: 0 0 2em 0;}
.dataTables_wrapper::-webkit-scrollbar-thumb{ background-color: transparent;}

.table>thead>tr>th { font-size: 13px; line-height: 15px; font-family: 'Poppins-Bold'; color: var(--head-text-color);}
.table>tbody>tr>td, .table>tbody>tr>td>p { font-family: 'Poppins-Medium'; font-size: 14px; line-height: 19.6px; color: var(--dark-text-color); border-top: 1px solid #E2E8F0; padding-block: 20px; }
.table>tbody>tr>td>a { font-family: 'Poppins-Medium'; font-size: 14px; line-height: 19.6px; color: var(--dark-text-color); padding-block: 20px; }

.listing_table>tbody>tr>td { border: 3px solid transparent; border-inline: 0; }
.listing_table tr:has(input:checked) td:first-child { border-left: 3px solid transparent; }
.listing_table tr:has(input:checked) td:last-child { border-right: 3px solid transparent; }

.table>thead>tr>th { border: 0;text-align: center;}

#edit-temp .table>tbody>tr>td, #edit-temp .table>tbody>tr>td>p { padding-block: 12px;}

.table>tbody>tr>td>p { padding: 0; border: 0; margin: 0;}
.listing_location_text, .limit, .email { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;}
.limit { width: 32em;}
.email { width: 15em;}
.name { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; margin: 0; width: 80px; }

.service_table .limit { width: 22em;}

.booking_sec .custom_table td { width: 12%;}
.booking_sec .limit { width: 14em; -webkit-line-clamp: 2;}
.booking_sec .btn_yellow.filter_btn { padding-inline: 12px;}
.booking_sec .btn_yellow.filter_btn:first-child { margin-right: 6px; }

p.listing_location_text { width: 15em;}

.table>tbody>tr>td i{ padding-inline: 10px; font-size: 10px;}
.table>tbody>tr>td i.fs-medium { font-size: 16px;}

.tabel_hdng_icon{ display: flex; justify-content: space-between; align-items: center;}

/* c */
div#myTable_length { display: none;}

div#myTable_filter {  display: none;}

td.form_btn button { border: none; background-color: transparent;text-align: start;}
.form_btn .dropdown-menu { min-width: 100%; translate: -80px 10px; border-radius: 5px; background: #FFF; width: 140px; box-shadow: 0px 3.52107px 14.96456px 5.28161px rgba(0, 0, 0, 0.04);}
.custom-table .form_btn .dropdown-menu, .lisiting_table .form_btn .dropdown-menu { width: 160px;}

.dropdown-item { text-align: center; display: block; color: black; padding: 10px; padding-inline: 21px; border-radius: 1.5px; cursor: pointer; width: 100%; font-size: 12px; font-family: 'Poppins'; font-weight: 600;}
td.form_btn button, td.form_btn a{text-align:start; padding-inline: 10px;}

.form_btn i.fa-solid.fa-ellipsis { font-size: 20px;}
/* .head_btn h1{ padding-bottom: 20px;} */
.custom_btns { display: flex; justify-content: space-between; align-items: center; gap: 10px;}

.form-group.has-feedback.has-search { background-color: white; width: 450px; height: 50px; display: flex; align-items: center; border-radius: 30px; border-radius: 15px; overflow: hidden; border: 0.5px solid #E2E8F0;}

.has-search button { padding-block: 15px; padding-inline: 15px; background-color: white; color: #2D3748;}
.has-search input { padding-block: 10px; width: 88%;}

i.fa-solid.fa-filter { float: right; padding-block: 19px; padding-inline: 20px;}

.notification-box .comment-widget .media {  padding: 20px 0px;  border-bottom: 1px solid lightgray;}
.notification-box .comment-widget .media:last-child { border: 0; }

.comment-widget .media .media-left { padding-right: 10px; padding-top: 1px;}

.card_wrapper { display: flex; /* justify-content: space-between; */ /* gap: 20px; */ flex-wrap: wrap; margin-right: -15px; margin-left: -15px; row-gap: 15px;}
.card_element { width: 25%; padding-inline-start: 15px; padding-inline-end: 15px;}
.card_parent { display: flex; justify-content: space-between; align-items: center; border-radius: 15px; gap: 10px; padding: 15px; /* flex-shrink: 0; */ flex: 0 0 20%;}
.card_inners { display: flex; flex-direction: column; /*align-items: center;*/ /*justify-content: center;*/ height: 50%; row-gap: 10px;}

.blue_card h1, .blue_card p { color: var(--white-color);}
.card_inners h1, .card_inners p { padding: 0; margin: 0; line-height: 1;}
.card_inners h1 { font-size: 20px;  word-break: break-all;}
.table_sect .table_wrraper .table-responsive { overflow: unset;}
.yellow_card_pill { background-color: var(--primary-color);}

.black_card_pill { background-color: var(--secondary-color);}
.card_pill { padding: 10px 16px; border-radius: 12px;}
.card_pill h1 { color: var(--white-color);}
.white_card { background-color: var(--white-color); box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07);}
.card_element p { color: #4A4A4A;}

.index .card_element { width: 49%;}

td.form_btn .form_yellow_btn>button { font-size: 10px; font-weight: 600; padding: 11px 25px 11px 25px; border-radius: 12px; background: var(--primary-color); color: var(--black_color); font-family: 'Poppins-Medium';}

td.form_btn .form_black_btn>button { font-size: 10px; font-weight: 600; padding: 11px 21px 11px 21px; border-radius: 12px; background: var(--black_color); color: var(--white-color); font-family: 'Poppins-Medium';}
.form_yellow_btn button:hover { border:1px solid var(--primary-color); background: transparent; color: var(--black_color); font-family: 'Poppins-Medium';}
.edit-profile-pic{ height: 45px  ; width: 45px; position: absolute; bottom: 3px; border-radius: 30px; right: 2px;}

.main-dic{ position: relative; width: 200px; height: 200px; margin-bottom: 20px;}

.form_black_btn button:hover { border:1px solid var(--black_color); background: transparent; color: var(--black_color); font-family: 'Poppins-Medium';}

.form_trans_btn {  font-size: 10px;  font-weight: 600; padding: 11px 28px 11px 28px; border-radius: 12px; background: transparent; border:1px solid var(--black_color); color: var(--black_color); font-family: 'Poppins-Medium';}
.form_trans_btn:hover { background: var(--black_color); color: var(--white-color); font-family: 'Poppins-Medium';}

.trans button,.trans select { font-size: 10px; font-weight: 600; padding: 11px 28px 11px 28px; border-radius: 12px; background: transparent; border:1px solid var(--black_color); color: var(--black_color); font-family: 'Poppins-Medium'; cursor: pointer;}
.trans button:hover{ background: var(--black_color); color: var(--white-color); font-family: 'Poppins-Medium';}
.trans_btn { border: 0; padding: 0; background-color: transparent; cursor: pointer; }

.form_blck_btn { font-size: 10px; font-weight: 600; padding: 11px 28px 11px 28px; border-radius: 12px; background: var(--black_color); color: var(--white-color); font-family: 'Poppins-Medium';}
.form_blck_btn:hover { border:1px solid var(--black_color); background: transparent; color: var(--black_color); font-family: 'Poppins-Medium';}

/* td.form_btn { display: flex; gap: 10px;} */

.form_field_padding input.form-control, .form_field_padding select.form-control { padding-inline: 22px; height: 50px; border-radius: 15px;}
.form_field_padding textarea.form-control { padding-inline: 22px; border-radius: 15px; resize: none;}
.form_field_padding { padding-block: 10px;}
.form_field_padding input[name=address] { padding-right: 50px;}
#reply-contact .form_field_padding .form-control { height: 40px;}

.white-box { border-radius: 20px; border: none; box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07); margin-top: 5px;}
.white-box.no_shadow { box-shadow: none; padding: 0; }
.inline { display: flex; justify-content: space-between;}
.table_wrraper .nav-pills>li.active>a{ background:var(--primary-color) ; color: #000;  border: 0 !important;}
.table_wrraper .nav-pills>li>a, .table_wrraper .nav-pills>li>a:hover { background: #EBEBEB ; color: #000 ; border: 0;}
.fa-plus:before { content: "\2b"; color: #161641; border: 2px solid #161641; font-size: 18px; border-radius: 10px; width: 30px; display: flex; justify-content: center; font-weight: 700;}
.parent-key{ display: flex; justify-content: space-between; margin-bottom: 15px;}

.table_wrraper .nav-pills>li.active>a:hover{ border: 0; background: transparent; color: black;}
.table_wrraper ul.nav.nav-pills { float: right; position: absolute; right: 45px; top: 30px;}

.nav-pills:not(.head_bt .nav-pills)>li+li { margin-left: 10px;}

.reject .modal-body textarea.form-control { box-shadow: 0px 4px 15px 1px #0000000A; padding-block: 10px; padding-inline: 22px; border-radius: 15px; border: none; resize: none;}

.modal-body { position: relative; padding-inline: 35px; padding-block: 30px; padding-top: 12px;}
.modal-title { text-align: center; padding-block: 20px;}
.modal_btn button.btn.yellow { width: 100%; border-radius: 12px; height: 40px;}
.mod_cust_text span:not(.info){ color: #000;}
.profile-image-div { width: 180px; height: 180px; overflow: hidden; border-radius: 50%;}
.profile-image-div { border-radius: 15%; position: relative; margin-block: 2em;}
img#blah { width: 100%; height: 100%; object-fit: cover; object-position: center;}
.profile-image-div img { max-width: 180px;}
.profile-image-div input[type=file] { padding: 10px; background: #2d2d2d;}
.profile-image-div input { position: absolute; top: 0; height: 100%; opacity: 0; z-index: 9999; cursor: pointer;}
.profile-image-div:after { content: ''; background: url(../website/images/camera.png) no-repeat center; width: 40%; height: 15%; display: block; top: 155px; left: 123px; position: absolute; /* background: url(); */ cursor: pointer; background-size: contain;}

.motoravenue_gallery { position: relative;}

.form-check { position: relative;  width: 14%;}
.motoravenue_gallery .file { visibility: none;}
.motoravenue_gallery input { position: absolute; width: 250px; top: 0; height: 100%; opacity: 0;}


/*img*/
label.form-check-label.files { height: 180px;}

.image_preview .img_pre {  display: flex; align-items: center; flex-wrap: wrap; gap: 1rem;}
.motoravenue_gallery p { padding-left: 5px;}
.image_preview .img_pre .image-container { width: 200px; height: 200px; position: relative; border-radius: 10px; overflow: hidden;}
.image_preview .img_pre .image-container>div:first-child { width: 100%; height: 100%;}
.image_preview .img_pre .image-container>div:first-child img { width: 100% !important; height: 100%; object-fit: cover; padding: 0;}
.image_preview .img_pre .image-container>div:nth-child(2) { position: absolute; left: 0; top: 0; z-index: 9; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 30px; opacity: 0; cursor: pointer;}

/* .image_preview .img_pre .image-container:hover {} */

.image_preview .img_pre .image-container:hover img { filter: brightness(0.5);}
.image_preview .img_pre .image-container:hover>div:nth-child(2) { color: white; opacity: 1;}

/*img*/

.radio-sec label { padding-left: 0;}
.radio-sec { display: flex; align-items: baseline; gap: 10px; padding-block: 10px; padding-left: 5px;}
i.fa-solid.fa-location-dot { position: absolute; top: 60px; right: 40px;}

/*select2*/
.sidebar,.slimScrollDiv{overflow: visible!important;}
.select2-results__option { padding-right: 20px; vertical-align: middle;}
.select2-results__option:before { content: ""; display: inline-block; position: relative; float: right; height: 20px; width: 20px; border: 1px solid #e5ebecbd; border-radius: 4px; background-color: #fff; /* margin-right: 20px; */vertical-align: middle;}
.select2-results__option[aria-selected=true]:before { font-family:fontAwesome; content: "\f00c"; color: #000; background-color: var(--primary-color); border: 1.65px solid #000;border-radius: 7px; display: inline-block; padding-left: 3px; font-size: 12px;}
.select2-container--default .select2-selection { background-color: white; border: 1px solid #e5ebecbd; border-radius: 13px;}
.select2-container--default .select2-results__option[aria-selected=true] { background-color: #fff;}
.select2-container--default .select2-results__option--highlighted[aria-selected] {  background-color: #eaeaeb; color: #272727;}
.select2-container--default .select2-selection { margin-bottom: 10px;}
.select2-container--default.select2-container--open.select2-container--below .select2-selection { border-radius: 13px;}
.select2-container--default.select2-container--focus .select2-selection { border-color: grey; border-width: 2px; width: 100%;}

.cust_select > .select2 { width: 100% !important; }
.cust_select span.select2-selection { border-radius: 14px; margin: 0; border: 0; }
.select2-container--default .select2-selection--single{border: none !important;}
.select2-container--default .select2-selection { border-width: 0px; /*height: 45px;*/}
.select2-container .select2-selection--single { height: unset !important;}
.select2-container--default .select2-selection--single .select2-selection__arrow { display: none;}
.select2-container--default .select2-selection .select2-selection__rendered { box-sizing: border-box; list-style: none; margin: 0; padding: 5px 9px; width: 100%;}

.select2-container--default .select2-search--inline .select2-search__field { background: transparent; border: none; outline: 0; box-shadow: none; padding: 3px 17px;}
.select2-container--open .select2-dropdown--below { border-radius: 6px; box-shadow: 0 0 10px rgba(0,0,0,0.5);}

.select2-selection .select2-selection--multiple:after { content: 'hhghgh';}
.select2-container--default .select2-results>.select2-results__options { max-height: 400px; overflow-y: auto; padding: 10px;}
/* select with icons badges single*/
.select-icon .select2-selection__placeholder .badge { display: none;}
.select2 { display: inline-grid; width: 100%;}

/*.select{appearance: none;}*/
.select-icon .select2-results__option:before,
.select-icon .select2-results__option[aria-selected=true]:before { display: none !important;}

.select-icon  .select2-search--dropdown { display: none;}
.select2 .fa-angle-down:before { content: "\f107"; position: absolute; top: 80px; z-index: 99999; right: 30px;}
.car_page .select2 .fa-angle-down:before { content: "\f107"; position: absolute; top: 60px; z-index: 99999; right: 30px;}
.car_page .select1 .fa-angle-down:before { content: "\f107"; position: absolute; top: 195px; z-index: 99999; right: 30px;}

span.select_text { padding: 15px 11px; font-size: 12px; font-family: 'Poppins-Medium'; color: #000;}
.plus_jq { display: flex; justify-content: space-between; padding-right: 20px;}

/*selectend*/

.head_bt .nav-pills>li.active>a {background: #ffce32;color: #4a4a4a;padding: 16px 40px 16px 40px;border-radius: 8px;border: none;border: 2px solid #ffce32;}
.head_bt .nav-pills>li>a { background: transparent; color: #000000; padding: 16px 40px 16px 40px; border-radius: 8px;}
.head_bt ul.nav.nav-pills { display: flex; gap: 10px;}
.head_bt .nav-pills>li>a:hover { background: #ffce32; border: none; padding: 16px 40px 16px 40px; color: black; border: 2px solid #ffce32;}

/*waqar css start*/

.center { text-align: center;}
.card_sect .customer_card .card_parent {  border-radius: 10px;  padding: 10px;}
.card_sect .customer_card .price_card { width: 20%;}
.testimonial_card .profile-widget { text-align: left;}
.testimonial_card .profile-widget h2 { padding-left: 10px;}
.testimonial_card .profile-widget .profile-img { padding-top: 0px;}
.testimonial_card .profile-widget .profile-img .img_round { height: 145px;}
.testimonial_card .map_icon{ color: #FFCE32; height: 22px;}

.testimonial_card .custom_design { display: flex; gap: 5px; align-items: baseline;}
.testimonial_card .profile-widget .user_img { display: flex; align-items: center;}
.testimonial_card .img_circle { border-radius: 50%; width: 60px;}

.testimonial_card .swiper { width: 100%; height: 100%;}
.testimonial_card .swiper-slide { text-align: center; font-size: 18px; background: #fff; display: flex; justify-content: center; align-items: center; width: 40% !important;}
.testimonial_card .swiper-slide img { display: block; object-fit: cover;}
.testimonial_card .card_text { text-align: left; padding-block: 14px;}
.testimonial_card .pink-box { background-color: #FFF9E4; padding: 14px; border-radius: 12px; width: 100%;}
.testimonial_card .custom_box { padding: 34px 19px !important;}
.testimonial_card .box { padding: 10px 14px !important;}
.testimonial_card .slider_reviews { display: flex; justify-content: space-between;}

.list_sec_service .custom_btns { display: flex; justify-content: end; align-items: baseline; gap: 17px;}
.list_sec_service .btn_yellow { padding: 19px 34px;}
.booking_sec_service .booking_service_search form.example, .list_sec_service .list_service_search form.example { display: flex; width: 600px;}

.booking_sec_service .booking_service_search input[type="text"],
.list_sec_service .list_service_search input[type="text"] { width: 100%;}
.box_head { display: flex; justify-content: space-between;}
.box_head a img { height: 45px; width: 45px;}
.d_btn-row button.btn { float: right; background: #4a4a4a; color: white; padding-block: 12px; padding-inline: 15px; border-radius: 15px; margin-bottom: 1em; font-size: 16px;}

.d_btn-row button.btn:hover { background: transparent; border: 1px solid #4a4a4a; color: #4a4a4a;}
.booking_sec_service .custom_btns { display: flex; justify-content: end; align-items: unset; gap: 17px;}
.booking_sec_service .filter_btn { padding: 4px 19px;}
.booking_sec_service button.sort_btn { padding: 19px 37px; background-color: var(--head-text-color); color: #ffffff;}

.search_sec .filter_data .item { display: inline-flex; gap: 10px;}
.search_sec .search_img { width: 60px; height: 55px;}
.search_img img { width: 100%; height: 100%; object-fit: cover; background-position: center; border-radius: 11px;}
.search_sec .list_search { border: 1px solid; padding-left: 20px;}
.search_sec .form-group.has-feedback.has-search  { height: 40px;}
.nav_search.main.d-flex .has-search { margin: 0; margin-left: 10px ;}

.calendar_sec .custom_btns { display: flex; justify-content: space-evenly; align-items: center;}
.calendar_sec .calendar_group{ left: 294px;}
.calendar_sec .calendar_group .calendar_btn { color: #ffff; background-color: var(--head-text-color);}

.calendar_sec .fc-day-header span { background: #ffce32; padding-block: 20px; padding-inline: 10px; border-radius: 10px; color: black;}
.calendar_sec .fc-day-top .fc-tue .fc-past span{ background-color: #ffce32; width: 90px; height: 50px; border-radius: 10px;}
.calendar_sec .fc th, .fc td { border-width: 1px; padding: 3px; vertical-align: top;}

/*waqar css end*/

/*medical form*/
.profile-image-div { position: relative; width: 180px; height: 180px; overflow: hidden;}
img#blah { width: 100%; height: 100%; object-fit: cover; object-position: center;}
.profile-image-div img { max-width: 180px;}
.profile-image-div input[type=file] { padding: 10px; background: #2d2d2d; cursor: pointer;}
.profile-image-div input { position: absolute; width: 100%; top: 0; height: 100%; opacity: 0; z-index: 9999; cursor: pointer;}
.profile-image-div:after { content: none;}
.Add_data .create_btn { width: 100%;}

/*medical Form*/
.cust_back { padding: 30px 20px; background: white; border-radius: 20px; margin-block: 1em;}

.added_experience p { font-size: 15px; color: black; line-height: 25px;}
.exp_det_body { display: flex; justify-content: space-between;}
.left_field li, .right_field li { padding-block: 4px; color: black;}
.sec_right_field li { padding-block: 4px; color: black;}

ul.sec_right_field { padding-top: 10px;}

.exp_detail { padding: 15px 0px 15px 0px; border-right: 1px solid lightgrey; padding-right: 20px;}
.Add_data p.btn.add_info_btn { width: 100%; padding: 12px; background: #ffce32; color: #4A4A4A; font-size: 16px; font-family: 'Poppins-Medium'; border-radius: 12px;}
.form_field_row h3 { font-family: 'Poppins-Medium'; font-size: 22px; font-weight: 500; line-height: 33px;}

/* --------------Inbox Page------------------ */

/* ---Global Classes--- */
.d-flex { display: flex; }
.flex-wrap { flex-wrap: wrap; }
.d-inline-block { display: inline-block;}
.align-items-center { align-items: center; }
.align-items-end { align-items: end; }
.justify-content-center { justify-content: center;}
.justify-content-between { justify-content: space-between;}
.justify-content-end{ justify-content: end;}
.flex-column { flex-direction: column;}
.right { float: right;}
.text-start { text-align: start;}

.bg_yellow { background-color: var(--light-yellow)}
.bg_white, .user-chat-container { background-color: var(--white-color); box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07); margin-top: 5px;}

.b_radius { border-radius: 33px;}
.b_border { border: 1px solid #BDBDBD;}
.b_shadow { box-shadow: 0px 0px 8px 0px rgba(189, 189, 189, 0.25);}
.img-fluid { height: auto; width: 100%;}
.d-inline-block { display: inline-block}
.d-none { display: none; }
.position-relative { position: relative;}

.me-0 { margin-right: 0 !important;}
.me-2 { margin-right: 20px;}
.mt-0 { margin-top: 0;}
.mb-0 { margin-bottom: 0;}
.pt-0 { padding-top: 0;}
.pb-0 { padding-bottom: 0;}
.pt-2 { padding-top: 10px;}
.pt-3 { padding-top: 20px;}

.border-0 { border: 0; }

.py-1  { padding-block: 5px; }
.py-2  { padding-block: 10px; }
.py-3  { padding-block: 15px; }
.py-4  { padding-block: 20px; }
.py-5  { padding-block: 25px; }
.pe-3  { padding-right: 20px; }

.w-100 { width: 100% !important;}
.w-75  { width: 75%; }
.w-65  { width: 65% !important; }
.w-50  { width: 50% !important;}
.w-25  { width: 25%; }

/*-------Transparent Scrollbar----------*/

.chat-sec-1 .chat_body::-webkit-scrollbar-thumb, .chat-sec-1 .all_chat::-webkit-scrollbar-thumb,  .chat-sec-1 .all_chat::-webkit-scrollbar-track,  .chat-sec-1 .all_chat::-webkit-scrollbar, .chat-sec-1 .chat_body::-webkit-scrollbar-track { background-color: transparent;}

.form-control::placeholder{color: #A0AEC0;}
.preview .close { background-color: #FFCE32;  padding: 6px 11px;  border-radius: 50%; font-size: 12px;  }
.error { display: none; }
.error p { color: rgb(255 113 113); font-size: 16px; padding: 10px;}

.cal_body .cal_title { padding-inline: 22px; height: 50px; border-radius: 15px; box-shadow: 0 8.568594932556152px 22.49256134033203px 0 rgba(0, 0, 0, 0.07);}
.cal_body .cal_date { padding: 5px 10px;}
.cal_body .cal_head { padding: 15px 0;}

.listing_location { width: 25%;}
.top-left-part .logo img { height: 74px; width: 180px; object-fit: contain;}
.table-responsive { overflow: visible;}
.custom_table img { height: 55px; object-fit: contain;}
.category img { mix-blend-mode: difference;}
.top-left-part { width: 264px;}
.custom-table .user_profile img { margin-right: 10px; }
.custom-table td.form_btn { padding-top: 34px !important;}
.iti { width: 100%;}
.iti__selected-flag { border-radius: 15px 0 0 15px;}

.pagination-info, .dataTables_info { display: none;}

.nav .open>a, .nav .open>a:focus, .nav .open>a:hover { background-color: transparent; border: 0;}

.cmsForms .d-flex  { align-items: center; gap: 10px;}
.cmsForms .d-flex  .fa-edit { padding-top: 22px; cursor: pointer; font-size: 18px; }
.cmsForms .d-flex .form_field_padding { width: 100%;}
.cmsForms .d-none { display: none;}
.cmsForms .files { cursor: pointer;}
.cmsForms input[disabled] + label { cursor: not-allowed;}
.cmsForms input[disabled] + label>img { filter: contrast(0.8);}
.cmsForms input + label>img { height: 120px; width: 100%;}
.amen .select2-container--default .select2-search--inline .select2-search__field { width: 480px !important;}

.mini-sidebar .sidebar .sidebar-nav #side-menu li a { padding-inline: 10px; overflow: visible;}
.mini-sidebar .logo img, .mini-sidebar .logo b { display: none !important;}
.nav-pills>li.active>a:focus, .nav-pills>li.active>a:hover { background-color: var(--primary-color) !important; border-color: var(--primary-color) !important;}
.create_amenity_option_sec .nav-pills>li.active>a {background-color: #FFCE32; border-color: var(--primary-color); color: #000;}
.create_amenity_option_sec .nav-pills>li:not(.active)>a:hover {background-color: #000; border: 2px solid #000; color: #fff;}
.sidebar-toggle i { background-color: #FFCE32; border-radius: 8px; padding: 10px; color: #000;}

.mini-sidebar .sidebar-nav #side-menu>li:hover>a { background-color: #FFCE32; border-radius: 12px;}
.favicon { display: none !important;}
.mini-sidebar  .favicon, .mini-sidebar  .favicon img { display: inline-block !important;}
.mini-sidebar .top-left-part { padding-left: 20px;}

.cms_btn .head_bt .nav-pills>li:not(.active)>a, .head_bt .nav-pills>li:not(.active)>a { border-color: #EBEBEB; background-color: #EBEBEB; }

#amenities_pane table tbody tr td .options_link:hover, #categories_pane table tbody tr td .options_link:hover {color: #ffce32;}

.amen input.form-control, .amen select.form-control, .amen .select2, .amen .selection { font-size: 15px; background-color: #fff; border-radius: 15px; border: 0; box-shadow: 0px 4px 15px 1px rgba(0, 0, 0, 0.04);}
.amen .modal-body { padding-block: 40px;}

/* Data Tables */
.dataTables_wrapper .sorting::after, .dataTables_wrapper th::after {display: none;}
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover, .pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover { border: 1px solid #FFCE32; background-color: #FFCE32; }
.dataTables_wrapper .dataTables_paginate .paginate_button:hover { background-color: var(--primary-color) !important; border-color: var(--primary-color);}
.pagination>li>a, .pagination>li>span, .pagination>li:first-child>a, .pagination>li:first-child>span, .pagination>li:last-child>a, .pagination>li:last-child>span { float: unset; border-radius: 3px;}

table.booking_table { width: 80%;}
table.booking_table td { padding: 5px 10px; }
table.booking_table td:nth-child(even) { color: #000; font-weight: 600;}
table.booking_table tr:last-child td:last-child { font-weight: 900;}

table.dataTable thead .sorting:not(:first-child), .booking_sec .dataTable thead .sorting { pointer-events: none;}

.semi-bold { font-weight: 600;}
.bold { font-weight: 700;}

.mb-3 { margin-bottom: 20px;}
.pt-4 { padding-top: 1.5rem;}
.py-1 { padding-block: 0.5rem;}
.me-3 { margin-right: 1rem;}
.padding { padding-block: 20px;}
.padding_block { padding-block: 10px;}
.p_top, .pt-2 { padding-top: 20px;}
.p_bottom, .pb-2 { padding-bottom: 20px;}

.ps-1 { padding-left: 5px;  }
.ps-2 { padding-left: 10px; }
.ps-3 { padding-left: 15px; }
.ps-4 { padding-left: 20px; }
.ps-5 { padding-left: 25px; }
.ps-6 { padding-left: 30px; }

.px-1 { padding-inline: 10px !important; }
.px-2 { padding-inline: 15px; }
.px-3 { padding-inline: 20px; }
.px-4 { padding-inline: 25px; }
.px-5 { padding-inline: 30px; }

.gap-1 { column-gap: 10px;}
.gap-2 { column-gap: 15px;}
.gap-3 { column-gap: 20px;}
.gap-4 { column-gap: 25px;}
.gap-5 { column-gap: 30px;}
.gap-6 { column-gap: 35px;}
.gap-7 { column-gap: 40px;}

.light_bold { font-weight: 500 !important;}
.fs-20 { font-size: 20px;}
.poppins { font-family: 'Poppins';}
.poppins-medium { font-family: 'Poppins-Medium';}
.poppins-semi-bold { font-family: 'Poppins-SemiBold';}

.notify-box .comment-widget .media { padding-bottom: 20px;}
.notify-box .white-box { min-height: 23.5em;}
.notify-box .white-box:has(.no-notification) { min-height: 11em;}

.navbar-top-links .noti_drop .dropdown-menu  li:last-child a { border-radius: 0 0 10px 10px;}
.navbar-top-links .noti_drop .dropdown-menu>li>a:focus, .navbar-top-links .noti_drop .dropdown-menu>li>a:hover { background-color: var(--primary-color);}

/* Alerts */
div:where(.swal2-container) div:where(.swal2-popup) { border-radius: 22px !important;}
div:where(.swal2-icon).swal2-warning, div:where(.swal2-icon).swal2-success .swal2-success-ring { border-color: var(--primary-color) !important; color: #fff !important; background-color: var(--primary-color) !important;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--primary-color) !important; color: var(--black) !important; border-radius: 7px !important; padding: 13px 40px 13px 41px;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-cancel { background-color: var(--black) !important; color: #fff;  border-radius: 7px !important; padding: 13px 40px 13px 40px;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus { box-shadow: none !important;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--primary-color); font-family: 'Poppins-Medium';}
.swal2-actions button { border-radius: 7px; font-family: 'Poppins-Medium'; }
div:where(.swal2-icon).swal2-success [class^=swal2-success-line] { background-color: #fff !important; z-index: 4 !important;}
body div:where(.swal2-icon).swal2-error { background-color: var(--primary-color) !important; border-color: var(--primary-color);}
body div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line] { background-color: #fff !important;}
body:has(#faqForm) .swal2-title { text-transform: uppercase;}

.swal2-timer-progress-bar { background-color: var(--primary-color) !important;}
body div:where(.swal2-container) div:where(.swal2-loader) { border-top-color: var(--primary-color); border-bottom-color: var(--primary-color);}
body div:where(.swal2-icon).swal2-info { border-color: var(--primary-color); color: var(--primary-color);}
body div:where(.swal2-container) div:where(.swal2-html-container) { font-size: 14px;}

/* Amenity */
/* input.select2-search__field { width: 500px !important;} */
.listing_stepper .select2-container--default .select2-selection--single .select2-selection__rendered { text-align: left;}
.listing_stepper .select2-container--default .select2-selection--single .select2-selection__placeholder { display: flex; justify-content: left;}
.amenity.popup .modal .modal-dialog .modal-content { min-height: 35em;}


/* Review Modal */

.review .search input.form-control { background-color: transparent;  border: none;  font-weight: 400; font-family: "Poppins"; font-size: 15px; color: var(--white);}
.review .modal-header{ border: none; display: flex; justify-content: center; gap: 20px; align-items: center;}
.review .modal-all {  max-width: 1250px;  margin: 4rem auto;  height: 600px;  overflow: hidden;  border-radius: 20px;  background-color: var(--white);  }
.review .modal-dialog {  max-width: 1000px;  margin-top: 0; /* height: 530px; */ /* overflow: hidden; */  border-radius: 20px;  }
.review .modal-content { /*overflow: scroll;*/  height: 600px;  border: none;  }
.review .btn-close {  width: 20px; height: 20px; background-color: transparent;  position: absolute; right: 18px; top: 20px;  }
.review .search input.form-control { color: var(--black)}
.leave_review input.chat-input { width: 65%; border-radius: 40px; padding: 10px 30px; height: 50px; outline: none; border: 1px solid var(--comment-grey);}
.leave_review button.button { padding: 0 25px; height: 45px;}
.leave_review .rating i { color: var(--light-yellow);}
.review .search { border-radius: 40px; border: 1px solid var(--grey);}
.review .modal-body { height: 480px; overflow-y: scroll;}
.review ::-webkit-scrollbar {  width: 10px;  background-color: var(--scrollbar); border-radius: 20px;  }
.review ::-webkit-scrollbar-track {  background-color: transparent;  }
.review ::-webkit-scrollbar-thumb {  background-color: var(--light-yellow);  border-radius: 20px;  }
.review ::-webkit-scrollbar-thumb:hover {  background-color: var(--primary-color);  }
.review .user_img { height: 50px; width: 50px; border-radius: 50%;}
.review .user_img img { height: 100%; width: 100%; object-fit: cover; border-radius: 50%;}

/* Suspend Modal */

.suspend .modal-title { font-size: 25px;}
.suspend button { padding: 15px 35px;}
.suspend p, .suspend h6 { font-family: 'Poppins';}
.suspend p { color: #4A4A4A;}

.suspend_modal .modal-dialog { max-width: 700px;}
.suspend_modal .modal_btn .btn_yellow { height: 55px; border: 1px solid var(--primary-color); }
.suspend_modal .modal_btn .btn_yellow:hover { background-color: #fff; }
.suspend_modal .price { color: #333b3f; }
.suspend_modal .table tr > :first-child { padding-left: 0;}
.suspend_modal .modal_btn { margin-top: 30px;}
.suspend_modal .modal_btn .btn { width: 48%;}
.suspend_modal.customer_suspend .modal_btn .btn { width: 48%;}


/* Cancellation Modal */

.modal.cancelation .deductProvider label { cursor: pointer; border: 1px solid #000; padding-block: 6px; width: 48%; text-align: center; border-radius: 10px; }
.modal.cancelation .deductProvider input:checked + label { background-color: var(--primary-color); border-color: var(--primary-color); }
.modal.cancelation .modal-body .percent_icon { left: 45px; }

/* Amenity Modal View */
.amenity.popup .modal .mod_cust_text .amen_img { height: 220px; width: 220px; margin-bottom: 10px;}
.amenity.popup .modal .mod_cust_text .amen_img img { height: 100%; object-fit: contain;}

/* Sort filter */

.filter_search.form-group.has-feedback.has-search { overflow: visible;}
.filter_search form { display: flex; justify-content: space-between; align-items: center;}
.filter_search .dropdown-menu { padding: 15px 20px; border-radius: 10px; left: unset; right: 0; min-width: 240px; }
.filter_search.form-group.has-feedback.has-search button { padding: 10px; margin-inline: 10px;}
.booking_search .filter_search.form-group.has-feedback.has-search button { font-size: 14px; color: var(--text-color-light-grey); }
.booking_search .filter_search.form-group.has-feedback.has-search button:hover { color: #000; }
.filter_search select.form-select { width: 100%; background-color: #f7f7f7; padding: 8px; border-radius: 5px; cursor: pointer;}
.filter_search select.form-select>option { background-color: #fefefe; }
.filter_search.form-group.has-feedback.has-search .button, .filter_search.form-group.has-feedback.has-search .button1 {  border-radius: 10px; padding: 8px 15px; margin: 15px auto 0;  width: 48%;  }
.filter_search.has-search .button { background-color: var(--primary-color);  }
.filter_search.has-search .button1 { background-color: #000; color: var(--primary-color);  color: #fff; }


/* Calendar */

.daterangepicker.show-calendar .drp-buttons, .daterangepicker.show-calendar .drp-buttons > *, .daterangepicker_input, .daterangepicker:before, .daterangepicker:after { display: none !important; }
.daterangepicker.show-calendar { border-radius: 18px;  border: 0; box-shadow: 0 0 15px -5px #8e8e8e;  padding: 15px; margin-top: 15px; right: 30px !important;}
.daterangepicker.show-calendar .calendar-table { border: 0; margin-bottom: 10px; }
.daterangepicker.show-calendar .calendar-table th { color: #bababa;}
.daterangepicker.show-calendar .calendar-table th, .daterangepicker .calendar-table td { height: 35px; width: 35px;}
.daterangepicker.show-calendar .calendar-table th.month { font-family: 'Poppins'; font-size: 16px; font-weight: 600; color: #000;}
.daterangepicker.show-calendar .calendar-table td.disabled, .daterangepicker option.disabled { text-decoration:  none; color: #999999bd;}
.daterangepicker.show-calendar .calendar-table td.in-range { background-color: #fcdf7352; }
.daterangepicker.show-calendar .calendar-table td.active, .daterangepicker .calendar-table td.active:hover { background-color: var(--primary-color);}

.daterangepicker.show-calendar .ranges { float: unset;}
.daterangepicker.show-calendar .ranges .applyBtn { border: 1px solid var(--primary-color);}
.daterangepicker.show-calendar .ranges .applyBtn:hover { background-color: #fff;}
.daterangepicker.show-calendar .ranges .custom_cal_btn {  padding: 15px 40px;}

/* Add Admins */

.addAdmin .form_field_padding input.form-control { width: 49%; margin-bottom: 15px; box-shadow: 0px 0px 4px 0px #0000001F;}
/* .addAdmin .form_field_padding input.form-control:nth-child(even) { margin-left: 10px;} */
.addAdmin select.form-select { width: 100%; box-shadow: 0px 0px 4px 0px #0000001F; margin-bottom: 15px; }



/* ----------NO USE NOW-------------------- */


/* -----------dropzone css----------------- */
.dropzone .dz-preview .dz-image img{margin-bottom: 0;width: unset;}
.dropzone .dz-preview .dz-error-mark{display: none;}
.dropzone .dz-preview.dz-error .dz-error-message {display: block;display: none;}

i.percent_icon { position: absolute; top: 60px; right: 5px; cursor: pointer; color: #000000a3; display: none; font-size: 10px;}
.price_change i { left: 40px; right: unset;}
.price_change .seasonal_price_val + i { left: 48px; bottom: 27px;}

.form-checkk .form-check-input {
    /* border: 0.5px solid red; */
    background-color: #fff;
    /* background-image: radial-gradient(#ffce32 50%, #fff 50%); */
    font-size: unset;
    outline: 2px solid #4A4A4A !important;
}

.form-checkk .form-check-input:checked[type=radio] {
    --bs-form-check-bg-image: unset;
    outline: 2px solid #ffce32 !important;
    background-image: radial-gradient(#ffce32 50%, #fff 50%);
}

.drop_photos h6, .drop_photos p { cursor: pointer;}
#img-preview img:not(img:first-child) { display: none;}


/* .form-checkk .form-check-input:checked[type=radio] {} */
.uploaded_files .files { background: linear-gradient(to bottom, #eee, #ddd);  color: #fff;padding: 40px 50px; border-radius: 20px;}

.listing_stepper .review-card >p>.list_price, .listing_stepper .review-card >p>.dist_price { font-size: 20px;}

/* Calendar list */
.search_sec .search_text { max-height: 59em;  overflow: auto;}

/* Admin Chat */

.user-chat-container .bubble {  border: 0; background-color: #fff; box-shadow: 0px 6px 11px rgba(18, 67, 105, 0.03); border-radius: 40px; padding: 8px 25px; display: flex; align-items: end;}
.user-chat-container #searchInput { background-color: #f7f7f7;  border-radius: 30px;}
.user-chat-container .chat-box { border: 0;}
/* .listing_stepper .custom-fieldset > fieldset { max-height: 88vh; overflow-x: hidden; overflow-y: auto;  padding-bottom: 20px;}
.listing_stepper .custom-fieldset > fieldset::-webkit-scrollbar { width: 0;} */

/*  Step form changes style ends here  */

/* Custom radio style starts from here */

.label_radios_wrapper {display: flex; justify-content: space-between; align-items: center;}
.radios_wrapper {display: flex; align-items: center; gap: 15px;}
.custom_radio {display: flex; gap: 15px; align-items: center;}
.custom_radio label {position: relative; padding-right: 30px}
.custom_radio input[type="radio"] {display: none}
.custom_radio label:before {content: ""; display: block; position: absolute; width: 19px; height: 19px; border: 2px solid black; right: 0; border-radius: 5px; top: 0;}
.custom_radio:has(input[type="radio"]:checked) label:before {background: #ffce32;}
.custom_radio:has(input[type="radio"]:checked) label:after {content: "\f00c"; font-family: "Font Awesome 5 Free"; font-weight: 900; display: block; position: absolute; width: 19px; height: 19px; left: 0; border-radius: 5px; top: 0; color: white; display: flex; justify-content: center; align-items: center; font-size: 13px}

/* .review-reoprt .custom_radio label { padding-left: 30px; } */
.review-reoprt .custom_radio label {padding-left: 30px; font-weight: 400; font-family: "Poppins-regular";}
.review-reoprt .custom_radio label:before { left: 0; right: unset; border-radius: 50px; }

/* Custom radio style ends here */

/* CKEditor */
.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar, .ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners { border-radius: 10px 10px 0 0 !important;}
.ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-blurred { border-radius: 0 0 10px 10px;}

/* Froala */
.form_field_padding:has(.fr-box) { padding-bottom: 20px;}
.form_field_padding .fr-box.fr-basic .fr-wrapper, .form_field_padding .fr-box.fr-basic .fr-element { min-height: 200px; }

.form_field_padding .fr-box.fr-basic li { list-style: unset;}


/* Dashboard fixings starts from here */
.dash_head_sec {margin-bottom: 20px;}
.dash_head_sec .title h1 {padding-bottom: 0; margin: 0;}

.add_amenity_category_modal #amenity_form input:not(.select2-search__field) {padding-inline: 22px; height: 50px; border-radius: 15px; box-shadow: none; color: #565656; border: 1px solid #e5ebec; background: white}
.add_amenity_category_modal #amenity_form .select2 {min-height: 50px; border-radius: 15px; box-shadow: none; color: #565656; border: 1px solid #e5ebec; background: white}
.add_amenity_category_modal #amenity_form .select2-container--default.select2-container--focus .select2-selection {border: 0;}

.create_amenity_option_sec .cancel_btn { font-size: 12px; font-weight: 600; font-family: 'poppins-SemiBold'; background: #4A4A4A; padding: 18px 40px; border-radius: 12px; color: #FFCE32; line-height: 15px; border: 0; }

/* Dashboard fixings ends here */

.cust_select.form_field_padding span.select2-selection { border: 1px solid #e5ebecbd;}
.cust_select.form_field_padding .select2-selection--single {  border: 1px solid #e5ebecbd !important; padding: 4px 16px; }
.cust_select.form_field_padding span.select2-selection:focus { border-color: #000;}

.select_parent { border: 1px solid #e5ebec; padding-right: 12px; border-radius: 15px;}
.select_parent:has(select[disabled]) { background-color: #eee; border-color: transparent; }
.select_parent select { background-color: transparent; box-shadow: none; border-color: transparent; border: 0; }

.d-none { display: none !important;}
.position-relative .percent-icon { position: absolute; left: 45px; top: 20px;}


/* Listing CMS */

.steps_parent .steps { padding-block: 25px;}
.steps_parent .steps:first-of-type { padding-top: 0; }
.steps_parent .steps.divider { border-width: 3px; border-color: var(--primary-color);}
.steps_parent .steps.divider:last-child { border: 0;}
.steps_parent .steps .heading { padding-bottom: 10px;font-size: 22px;max-width: fit-content;/* font-family: 'Poppins'; *//* font-weight: 500; */}
.steps_parent .steps .form-group { margin-inline: 0;}
.steps_parent .steps .label_heading { padding: 0; margin-bottom: 0; font-size: 14px; }
.steps_parent .steps .dropify-wrapper { border-radius: 20px;}

.steps_parent .accordion-item { border: 1px solid var(--primary-color); margin-bottom: 20px; border-radius: 15px; overflow: hidden;}
.steps_parent .accordion-item a { width: 100%; text-align: start; height: 100%; position: relative; color: #000; text-decoration: none; padding: 15px 28px; font-size: 18px; transition: 0.3s ease-in; }
.steps_parent .accordion-item .accordion-body { padding: 15px 15px 0; }
.steps_parent .accordion-item a[aria-expanded="true"], .steps_parent .accordion-item a:hover { background-color: var(--primary-color); transition: 0.3s ease-in;}
.steps_parent .accordion-item a:after { content: ''; border: 3px solid var(--primary-color); position: absolute; right: 25px; top: 0; bottom: 0; margin: auto 0; display: block; height: 15px; width: 15px; rotate: 45deg; border-width: 0 2px 2px 0; transition: 0.5s ease-in;}
.steps_parent .accordion-item a[aria-expanded="true"]:after { border-color: #fff; rotate: 225deg; transition: 0.5s ease-in;}
.steps_parent .accordion-item a:hover:after { border-color: #fff; transition: 0.5s ease-in; }

/* Index page fixing */
.dash_index_report_notification_sec {padding-bottom: 0;}
.dash_index_report_notification_sec .col_left .table_wrraper {height: 329px; overflow-y: auto; padding-bottom: 20px;}
.dash_index_report_notification_sec .col_left .table_wrraper table {margin-bottom: 0}
.dash_index_report_notification_sec .col_right .comment-widget .media-list {height: 240px; overflow-y: auto; padding-right: 10px;}

.listing_type_index_sec .panel { background: transparent;}
.listing_type_index_sec .panel .panel-body { padding: 0;}
.listing_type_index_sec #category_types .box-title, .listing_type_index_sec #category_cms .box-title { font-weight: 600;  font-size: 18px; font-family: "Poppins-SemiBold"; margin-top: 10px; text-transform: unset;}

.listing_type_index_sec .panel ul li.active a{ background: #ffce32; color: #000000; border-radius: 8px; border: 0; }
.listing_type_index_sec .panel ul li a { border-color: #EBEBEB; background-color: #EBEBEB; color: #000000; padding: 16px 40px 16px 40px; border-radius: 8px; border: 0; }

/* Tooltip */
.info_tooltip { border: 2px solid var(--primary-color); border-radius: 50%; height: 25px; width: 25px; background-color: transparent; margin-inline: 10px; text-align: center; color: var(--primary-color); font-weight: 600;}

.select2-container--open .select2-dropdown--above {   border-radius: 12px;  border-bottom-left-radius: 0;  border-bottom-right-radius: 0;}
.select2-container--default .select2-results__option {color: #000; }
.select2-container--default .select2-results__option--highlighted[aria-selected] { background-color: #ffff0061;}
.select2-container .select2-dropdown .select2-results__options .select2-results__option[aria-selected="true"] {color: #000;  background-color: #ffce32;}

#cancelation #refundValue {color: black; font-weight: 600; line-height: 100px;}
#cancelation #refundValue:focus {color: black;}

/* Multi select2 tags UI according to theme */

.amenity_category_main_sec .select2-container--default .select2-selection--multiple .select2-selection__choice, .review_filter_modal .select2-container--default .select2-selection--multiple .select2-selection__choice { background-color: #ffce32; color: black; border: 1px solid #ffce32; border-radius: 8px; padding: 4px 8px; }
.amenity_category_main_sec .select2-container--default .select2-selection--multiple .select2-selection__choice__remove, .review_filter_modal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {color: black;}

/* Multi select2 tags UI according to theme */

.reviews_index_topbar .head_bt { flex: 1; }
.reviews_index_topbar .nav_search { flex: 1; }
.reviews_index_topbar .nav_search .filter_search { flex: 1; }
.reviews_index_topbar .nav_search .review_filter_btn i { margin-left: 5px; }

.rating-option-sinlge .rating_star_wrapper label { float: right; color: #bebebe; margin-right: 20px; }
.rating-option-sinlge .rating_star_wrapper input:checked ~ label > i { color: var(--btn-yellow); }
.rating-option-sinlge .rating_star_wrapper input:checked ~ label i { color: var(--btn-yellow); }
.edit_review .rating-option-sinlge .rating_star_wrapper label:hover ~ label i, .edit_review .rating-option-sinlge label:hover i {  color: var(--light-yellow);}

.new_listing { font-size: 16px; }

/* Review modal */

.property-image,.property-asset { display: inline-block; border-radius: 15px; overflow: hidden; width: 286px; height: 160px; }
.property-image img.img-fluid, .property-image img { width: 100%; height: 100%; object-fit: cover; border-radius: 20px; }
.property-asset { display: flex;  gap: 30px; width: 100%; }
.main_cart { display: flex;  align-items: start; gap: 20px; padding: 12px; box-shadow: -4px 4px 30px #00000014;  border-radius: 20px;  margin-block: 1em;  }
.main_cart .list_product_heading { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; margin: 0; height: fit-content; width: 80%; font-size: 18px; }

.review .provider-name, .review .dates-wrapper p, .rating-option-single .rating_title { color: #000; font-family: 'Poppins';}
.review .provider-name, .rating-option-single * { font-family: 'Poppins';}
.review .rating { color: #000;}

#edit_review_modal.modal .modal-body {padding-top: 0;}
#edit_review_modal.modal .review_comment .chat-input {flex: 1; border-radius: 20px;}
#edit_review_modal.modal .review_comment button[type="submit"]:not([disabled]) {background-color: #ffce32; border: 1px solid #ffce32;}
#edit_review_modal.modal .review_comment button[type="submit"][disabled] {cursor: not-allowed;}

.list_product_heading { color: #000; }
.review_comment { margin-top: 20px; }
.review_comment * { font-family: 'Poppins'; }
.review_comment .button { background-color: var(--primary-color); padding: 8px 20px; border-radius: 50px; color: #000; }

.delete_reply { background-color: #ffffffb3; padding: 5px 8px;  border-radius: 50%;}
.delete_reply i { color: red; }

/* Images uploader style starts from here */

/* .listing_stepper .add_photos_step .inner_section_main_col {max-width: 70%; margin: 0 auto;} */
.drag_drop_photos_wrapper .add_photo_box {height: 400px; width: 100%; border: 2px dashed #c2c2c2; display: flex; flex-direction: column; justify-content: center; padding: 20px; gap: 80px}
.drag_drop_photos_wrapper .add_photo_box .photo_icon {width: 110px; height: 110px; overflow: hidden; margin: 0 auto;}
.drag_drop_photos_wrapper .add_photo_box .photo_icon img {width: 100%; height: 100%; object-fit: contain; object-position: center;}
.drag_drop_photos_wrapper .add_photo_box .add_photo_btn label {color: #4A4A4A; font-family: Poppins; font-size: 13px; font-style: normal; font-weight: 600; line-height: 0; border-radius: 33px; background: #ffce32; padding: 25px 30px; cursor: pointer;}
.drag_drop_photos_wrapper .add_photo_box .add_photo_btn input[type="file"] {display: none;}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn { text-align: center;}

.drag_drop_photos_wrapper {display: flex; flex-wrap: wrap; gap: 20px 15px; /*max-height: calc(100vh - 420px);*/ overflow: hidden; overflow-y: auto; padding-right: 8px;}
.drag_drop_photos_wrapper .drag_drop_photo_single {flex: 0 0 23.5%; overflow: visible; border-radius: 20px; height: 200px; overflow: hidden;}
.drag_drop_photos_wrapper .drag_drop_photo_single img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single:first-child {flex: 0 0 100%; height: 450px;} */
.drag_drop_photos_wrapper:not(:has(.drag_drop_photo_single:nth-child(2))) .drag_drop_photo_single:first-child {flex: 0 0 100%; height: 100%;}
/* .drag_drop_photos_wrapper:not(:has(.drag_drop_photo_single:nth-child(2))) .drag_drop_photo_single:first-child {height: 100%;} */
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn .plus_icon_lbl {display: none}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .photo_icon {display: none;}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .plus_icon_lbl {display: block;}
.drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .add_photos_lbl {display: none;}

.drag_drop_photos_wrapper .drag_drop_photo_single, .listing_stepper .add_videos_step .video_wrapper .video_single {position: relative;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown, .listing_stepper .add_videos_step .video_wrapper .video_single .delete, .drag_drop_photos_wrapper .drag_drop_photo_single .bulk_del {position: absolute; right: 15px; top: 15px; z-index: 9;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown button, .listing_stepper .add_videos_step .video_wrapper .delete button  {background-color: #ffffffb3; color: #000; border-radius: 50%; box-shadow: 0 0 10px #0000000d; backdrop-filter:blur(2px); padding: 0; width: 35px; height: 35px; font-size: 15px;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu, .listing_stepper .add_videos_step .video_wrapper .video_single .delete .dropdown-menu {border-radius: 15px; box-shadow: 0 0 10px #0000000d; padding: 0;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li:first-child a, .listing_stepper .add_videos_step .video_wrapper .video_single .delete .dropdown-menu a {border-top-left-radius: 15px; border-top-right-radius: 15px;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single:first-child .dropdown-menu li .make_cover_btn {display: none;} */
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li:last-child a {border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;}
.drag_drop_photos_wrapper .drag_drop_photo_single:first-child .dropdown-menu li a, .listing_stepper .add_videos_step .video_wrapper .delete .dropdown-menu a:hover {border-radius: 15px;}
.drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li a {font-family: "Poppins"; font-size: 13px; text-align: left; font-weight: 500; color: #000; padding: 9px 20px;}
/* .drag_drop_photos_wrapper .drag_drop_photo_single:first-child:not(:last-child):before { content: "Cover Photo"; display: block; position: absolute; top: 10px; left: 10px; background: #fffc; backdrop-filter: blur(5px); padding: 8px 15px; color: #000; font-family: 'Poppins'; font-weight: 500; border-radius: 10px; box-shadow: 0 0 10px #0000000d; font-size: 14px;} */

.drag_drop_photos_wrapper .add_photo_box .add_photo_btn .plus_icon_lbl {background: none;}
.drag_drop_photos_wrapper .add_photo_box .plus_icon_lbl i:before {border: 0; font-size: 50px; color: #a4a4a4;}

#review_filter_modal .select2-container--default .select2-search--inline .select2-search__field { width: 100% !important;}

#view_review_modal .images_wrapper { display: flex; flex-wrap: wrap; gap: 20px 15px; /* max-height: calc(100vh - 420px); */ overflow: hidden; overflow-y: auto; padding-right: 8px; margin-top: 20px;}
#view_review_modal .images_wrapper .image_wrapper {flex: 0 0 23.5%; overflow: visible; border-radius: 20px; height: 160px; overflow: hidden;}
#view_review_modal .images_wrapper .image_wrapper img {width: 100%; height: 100%; object-fit: cover; object-position: center;}

#edit_review_modal .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown ul.dropdown-menu {left: unset; right: 0;}

#edit_review_modal .rating_star_wrapper { display: flex; /*width: 100%; justify-content: center; overflow: hidden;*/ flex-direction: row-reverse; /*height: 150px;*/ position: relative; }
#edit_review_modal .rating_star_wrapper > input {display: none;}
#edit_review_modal .rating_star_wrapper > label { cursor: pointer; width: 25px; height: 25px; margin-top: auto; background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='126.729' height='126.73'%3e%3cpath fill='%23e3e3e3' d='M121.215 44.212l-34.899-3.3c-2.2-.2-4.101-1.6-5-3.7l-12.5-30.3c-2-5-9.101-5-11.101 0l-12.4 30.3c-.8 2.1-2.8 3.5-5 3.7l-34.9 3.3c-5.2.5-7.3 7-3.4 10.5l26.3 23.1c1.7 1.5 2.4 3.7 1.9 5.9l-7.9 32.399c-1.2 5.101 4.3 9.3 8.9 6.601l29.1-17.101c1.9-1.1 4.2-1.1 6.1 0l29.101 17.101c4.6 2.699 10.1-1.4 8.899-6.601l-7.8-32.399c-.5-2.2.2-4.4 1.9-5.9l26.3-23.1c3.8-3.5 1.6-10-3.6-10.5z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: center; background-size: 76%; transition: .3s; }
#edit_review_modal .rating_star_wrapper > input:checked ~ label, .rating > input:checked ~ label ~ label { background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='126.729' height='126.73'%3e%3cpath fill='%23fcd93a' d='M121.215 44.212l-34.899-3.3c-2.2-.2-4.101-1.6-5-3.7l-12.5-30.3c-2-5-9.101-5-11.101 0l-12.4 30.3c-.8 2.1-2.8 3.5-5 3.7l-34.9 3.3c-5.2.5-7.3 7-3.4 10.5l26.3 23.1c1.7 1.5 2.4 3.7 1.9 5.9l-7.9 32.399c-1.2 5.101 4.3 9.3 8.9 6.601l29.1-17.101c1.9-1.1 4.2-1.1 6.1 0l29.101 17.101c4.6 2.699 10.1-1.4 8.899-6.601l-7.8-32.399c-.5-2.2.2-4.4 1.9-5.9l26.3-23.1c3.8-3.5 1.6-10-3.6-10.5z'/%3e%3c/svg%3e"); }
#edit_review_modal .rating_star_wrapper > input:not(:checked) ~ label:hover,
#edit_review_modal .rating_star_wrapper > input:not(:checked) ~ label:hover ~ label { background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='126.729' height='126.73'%3e%3cpath fill='%23d8b11e' d='M121.215 44.212l-34.899-3.3c-2.2-.2-4.101-1.6-5-3.7l-12.5-30.3c-2-5-9.101-5-11.101 0l-12.4 30.3c-.8 2.1-2.8 3.5-5 3.7l-34.9 3.3c-5.2.5-7.3 7-3.4 10.5l26.3 23.1c1.7 1.5 2.4 3.7 1.9 5.9l-7.9 32.399c-1.2 5.101 4.3 9.3 8.9 6.601l29.1-17.101c1.9-1.1 4.2-1.1 6.1 0l29.101 17.101c4.6 2.699 10.1-1.4 8.899-6.601l-7.8-32.399c-.5-2.2.2-4.4 1.9-5.9l26.3-23.1c3.8-3.5 1.6-10-3.6-10.5z'/%3e%3c/svg%3e"); }

#edit_review_modal .review_comment .chat-input {border: 1px solid var(--light-grey); padding: 10px 20px; font-size: 14px; color: black;}

.upload_doc_download .uploaded_doc_wrapper {display: flex; flex-wrap: wrap; align-items: center; gap: 15px}
.upload_doc_download .uploaded_doc_wrapper .single_doc {flex: 0 0 31.5%; width: 32.5%; padding: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.05); border-radius: 10px;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .doc {display: flex; flex-direction: column;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .doc .image_wrapper {width: 100%; height: 250px; overflow: hidden; position: relative;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .doc .image_wrapper img {width: 100%; height: 100%; object-fit: cover; border-radius: 10px;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .doc .image_wrapper canvas {width: 100%; height: 100%; object-fit: contain; border-radius: 10px;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .doc .doc_title {word-break: break-all; color: black; font-family: "Poppins"; font-size: 14px; margin-top: 10px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical; height: 40px;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .menu_doc {position: absolute; top: 0; left: 0; right: 0; bottom: 0; margin: auto; display: flex; align-items: center; justify-content: center; z-index: 99}
.upload_doc_download .uploaded_doc_wrapper .single_doc .menu_doc i {font-size: 60px; opacity: 0; transition: 0.4s ease;}
.upload_doc_download .uploaded_doc_wrapper .single_doc:hover .menu_doc i {opacity: 1; transition: 0.4s ease;}
.upload_doc_download .uploaded_doc_wrapper .single_doc .image_wrapper:after {content: ''; display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); border-radius: 10px; opacity: 0; transition: 0.4s ease;}
.upload_doc_download .uploaded_doc_wrapper .single_doc:hover .image_wrapper:after {opacity: 1; transition: 0.4s ease;}

.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn_wrapper {position: absolute; right: 15px; top: 15px; z-index: 9;}
.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn_wrapper .delete_btn {background-color: #ffffffb3; color: red; border-radius: 50%; box-shadow: 0 0 10px #0000000d; backdrop-filter:blur(2px); padding: 0; width: 35px; height: 35px; font-size: 15px; display: flex; align-items: center; justify-content: center;}

.lisiting_table .search_btns { justify-content: end; max-width: 75%; }

.modal.booking_cancellation .modal-body .refundValue_wrapper .percent_icon { width: 12px; top: unset; bottom: 15px;}

.review .review_comment .comment_wrapper {flex: 1;}
.review .review_comment .comment_wrapper .ck-editor .ck-content {border-radius: 15px;}
.review .review_comment .comment_wrapper .ck-editor .ck-content p {font-size: 14px; font-weight: 400; line-height: 1.5; color: black; font-family: 'Poppins';}
.review .review_comment .comment_wrapper .ck-editor .ck-toolbar {border: 0;}
.review .review_comment button[type="submit"] {width: 100%;}
.review .review_comment .ck-editor__editable {min-height: 150px !important;}

.review .ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-blurred, .review textarea.form-control { border-radius: 15px; resize: none; }
.review .review_comment .comment_wrapper #char-counter {text-align: right; margin-top: 5px; font-size: 16px;}
.review .leave_review .images_wrapper {margin-top: 16px; margin-bottom: 16px;}

/* E-Wallet */
.accordion_heading { font-size: 20px; color: #000; width: 100%; height: 100%;}
.accordion_heading:not(.collapsed) .acc_icon i { rotate: 180deg; transition: 0.3s ease-in;}
.accordion_heading .acc_icon i { transition: 0.3s ease-in; cursor: pointer;}

.payment-sec .btn_yellow { padding: 14px 40px; }
.tabs_sec .nav li a { background-color: #EBEBEB; padding: 12px 20px; border-radius: 12px; color: #000;}
.tabs_sec .nav li:hover a { color: #000; }
.pink_btn { background-color: #E41D56; color: #fff; }

.payment-sec .btn_yellow:not(.active .btn_yellow, .btn_yellow:focus) { background-color: #EBEBEB;}
body:has(#autoSchedule:is(.in)) .btn_yellow.auto, body:has(#request_wd:is(.in)) .btn_yellow.manual, .tabs_sec .nav li.active a { background-color: var(--primary-color);}

body:has(#autoSchedule:is(.in), #request_wd:is(.in)) .tabs_sec .nav li.active a { background-color: #EBEBEB; }


[tooltip] {position: relative;}
[tooltip]::before, [tooltip]::after { text-transform: none; font-size: .9em; line-height: 1; user-select: none; pointer-events: none; position: absolute; display: none; opacity: 0; }
[tooltip]::before { content: ''; border: 5px solid transparent; z-index: 1001; }
[tooltip]::after {content: attr(tooltip); font-family: Helvetica, sans-serif; text-align: center; min-width: 3em; max-width: 21em; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; padding: 1ch 1.5ch; border-radius: .3ch; box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35); background: #333; color: #fff; z-index: 1000; }
[tooltip]:hover::before, [tooltip]:hover::after { display: block; }
[tooltip='']::before, [tooltip='']::after { display: none !important; }

[tooltip]:not([flow])::before, [tooltip][flow^="up"]::before { bottom: 100%; border-bottom-width: 0; border-top-color: #333; }
[tooltip]:not([flow])::after, [tooltip][flow^="up"]::after { bottom: calc(100% + 5px); }
[tooltip]:not([flow])::before, [tooltip]:not([flow])::after, [tooltip][flow^="up"]::before, [tooltip][flow^="up"]::after { left: 50%; transform: translate(-50%, -.5em); }

[tooltip][flow^="down"]::before { top: 100%; border-top-width: 0; border-bottom-color: #333; }
[tooltip][flow^="down"]::after {top: calc(100% + 5px);}
[tooltip][flow^="down"]::before, [tooltip][flow^="down"]::after { left: 50%; transform: translate(-50%, .5em); }

[tooltip][flow^="left"]::before { top: 50%; border-right-width: 0; border-left-color: #333; left: calc(0em - 5px); transform: translate(-.5em, -50%); }
[tooltip][flow^="left"]::after { top: 50%; right: calc(100% + 5px); transform: translate(-.5em, -50%); }

[tooltip][flow^="right"]::before { top: 50%; border-left-width: 0; border-right-color: #333; right: calc(0em - 5px); transform: translate(.5em, -50%); }
[tooltip][flow^="right"]::after { top: 50%; left: calc(100% + 5px); transform: translate(.5em, -50%); }

@keyframes tooltips-vert {
    to {
        opacity: .9;
        transform: translate(-50%, 0);
    }
}

@keyframes tooltips-horz {
    to {
        opacity: .9;
        transform: translate(0, -50%);
    }
}

[tooltip]:not([flow]):hover::before, [tooltip]:not([flow]):hover::after, [tooltip][flow^="up"]:hover::before, [tooltip][flow^="up"]:hover::after, [tooltip][flow^="down"]:hover::before, [tooltip][flow^="down"]:hover::after { animation: tooltips-vert 300ms ease-out forwards; }
[tooltip][flow^="left"]:hover::before, [tooltip][flow^="left"]:hover::after, [tooltip][flow^="right"]:hover::before, [tooltip][flow^="right"]:hover::after { animation: tooltips-horz 300ms ease-out forwards; }


/* Spinner */
.spinner, .review .loader { border: 4px solid #f3f3f3; border-top: 4px solid #000; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite;  margin: 100px auto;}
@keyframes spin {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn_delete { color: #fff; border: 2px solid #e51212; background-color: #e51212; padding: 12px 30px;}
.notification-box .btn_delete { padding-block: 16px;}

.upload_doc_download .uploaded_doc_wrapper .single_doc .doc_preview { object-fit: contain;}

.listing_filters_wrapper {display: flex; gap: 10px; justify-content: flex-end;}
.custom_radio_wapper input[type="radio"] {display: none;}
.custom_radio_wapper label, .filter_btn_wrapper .filter_btn {font-family: "Poppins-SemiBold"; font-size: 12px; line-height: 15px; color: var(--head-text-color); border-radius: 12px; border: none; background-color: #EBEBEB; padding: 12px 20px; width: 100px; text-align: center; cursor: pointer;}
.listing_filters_wrapper .custom_radio_wapper label {width: max-content;}
.custom_radio_wapper:has(input[type="radio"]:checked) label, .filter_btn_wrapper .filter_btn.active {background-color: var(--primary-color);}
.filter_btn_wrapper .filter_btn { width: unset; padding-inline: 30px; box-shadow: none;}

.listing_filters_wrapper { justify-content: center; }

/* E-Wallet */

.earning_key_wrapper li .key { display: inline-block; height: 20px; width: 20px; border-radius: 50%; margin-right: 10px; background-color: #A2DE5B; vertical-align: middle;}
.earning_key li:nth-child(2) .key { background-color: #e65862;}
.earning_key li:nth-child(3) .key { background-color: #2f2e2e5a;}
.earning_key2 li:nth-child(1) .key { background-color: #D16DFF;}
.earning_key2 li:nth-child(2) .key { background-color: #446CB0;}
.earning_key2 li:nth-child(3) .key { background-color: #ff7932;}
.earning_key2 li:nth-child(4) .key { background-color: #ffce32;}


/* Listing Card View */

/* .listing_table .draft:not(.listing_parent_wrapper:has(#drafts:checked) .listing_table .draft) { display: none; }
.listing_parent_wrapper:has(#drafts:checked) .listing_table tbody tr:not(.draft, tr:has(.dataTables_empty)) { display: none; }
.listing_parent_wrapper:has(#drafts:checked) .listing_table .no_draft  { display: none; }
.listing_parent_wrapper:not(:has(#drafts:checked)) .listing_table .for_draft, .listing_parent_wrapper:not(:has(#drafts:checked)) .draft_text  { display: none; }

.listing_parent_wrapper:not(:has(#drafts:checked)) .card_view .draft { display: none;}
.listing_parent_wrapper:has(#drafts:checked) .card_view > .col-md-3:not(.draft) { display: none;} */

.listing_parent_wrapper .card_view .days-left:not(.draft .days-left) { display: none;}

button.close { opacity: 1;}




.card_view { display: none; padding-top: 35px;}
.card_view.d-block { display: block; }
.view_change { cursor: pointer; min-width: 60px; text-align: center; background-color: var(--primary-color); padding: 14px; border-radius: 10px; color: #000; font-size: 14px; font-family: 'Poppins-SemiBold'; }
.text-black-50 { color: var(--text-color-dark-grey); }

.listing_card_wrapper .listing_card { background-color: #fff; margin-bottom: 30px; min-height: 450px; border-radius: 10px; transition: 0.3s ease-in; border: 2px solid var(--primary-color);}
.listing_card_wrapper .listing_card.draft_card { min-height: 330px;}
.listing_card_wrapper .listing_card .card-img { height: 240px; width: 100%; overflow: hidden;  border-radius: 8px 8px 0 0;}
.listing_card_wrapper .listing_card .card-img img { height: 100%; width: 100%; object-fit: cover; transition: 0.5s ease-in;}

.listing_card_wrapper .listing_card:hover { border-color: #000; transition: 0.3s ease-in; }
.listing_card_wrapper .listing_card:hover .card-img img { scale: 1.1; transition: 0.5s ease-in; }
.listing_card_wrapper .listing_card .card-img img[src$="favicon_white_bg.svg"] { object-fit: contain;}

.listing_card_wrapper .listing_card .card-body { padding: 15px;}
.listing_card_wrapper .listing_card .card-body .title { font-size: 18px; color: #000; font-weight: 600; line-height: 1.2; min-height: 24px; }
.listing_card_wrapper .listing_card .card-body p { font-size: 16px; font-weight: 400; color: #000; line-height: 1.5; font-family: 'Poppins'; }
.listing_card_wrapper .listing_card .card-body .listing_info { margin-bottom: 5px; }
.listing_card_wrapper .listing_card .card-body .internal-name, .listing_card_wrapper .listing_card .card-body .title { display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; }
.listing_card_wrapper .listing_card .card-body .title { word-break: break-all; }
.listing_card_wrapper .listing_card .card-body .listing_info i { min-width: 16px; color: #4A4A4A; }

.listing_card_wrapper .listing_card .card-header .badge, .listing_card_wrapper .listing_card .card-header .dropdown { top: 10px;}
.listing_card_wrapper .listing_card .card-header .badge { left: 10px; padding: 5px 15px; }
.listing_card_wrapper .listing_card .card-header .dropdown { right: 10px; }
.listing_card_wrapper .listing_card .card-header .dropdown > button { padding: 2px 6px; border-radius: 5px; background-color: transparent;}
.listing_card_wrapper .listing_card .card-header .dropdown > button i { color: var(--primary-color); font-size: 20px;}
/* .listing_card_wrapper .listing_card .card-header .dropdown > button:hover { background: rgba(255, 255, 255, 0.2); box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); } */
.listing_card_wrapper .listing_card .card-header .dropdown .dropdown-menu {  border-radius: 10px; right: 0; left: unset;}
.listing_card_wrapper .listing_card .card-header .dropdown .dropdown-menu .dropdown-item { padding-block: 10px; background-color: transparent; font-weight: 600; font-family: 'Poppins'; width: max-content;}
.listing_card_wrapper .listing_card .card-header .dropdown .dropdown-menu .dropdown-item:last-child { font-weight: 700;}

.listing_card_wrapper .listing_card .card-header .badge p { color: #fff; text-transform: capitalize; letter-spacing: 0.7px; font-size: 14px; font-family: 'Poppins';  font-weight: 400;}
.listing_card_wrapper .listing_card .card-header:has(.publish) .badge { background-color: #387031;}
.listing_card_wrapper .listing_card .card-header:has(.status_name) .badge { background-color: #7a7b79;}
.listing_card_wrapper .listing_card .card-header:has(.paused, .cancel) .badge { background-color: #e74a25;}
.listing_card_wrapper .listing_card .card-header:has(.draft) .badge { background-color: #0283cc;}


.listing_table tr:has(input:checked) td { /* background-color: var(--primary-color); */ border-color: var(--primary-color); border-inline: 0;}
.listing_table tr:has(input:checked) td:first-child { border-left: 3px solid var(--primary-color); border-radius: 10px 0 0 10px; }
.listing_table tr:has(input:checked) td:last-child { border-right: 3px solid var(--primary-color); border-radius: 0 10px 10px 0; }

.lisiting_table .custom_btns .btn_yellow, .rounded_btn .nav-pills > li > a, .rounded_btn .nav-pills > li.active > a { border-radius: 100px; }

.lisiting_table .listing_parent_wrapper .dropdown-menu {min-width: 185px; left: unset; right: -70px;}
.lisiting_table .listing_parent_wrapper .dropdown-menu a, .lisiting_table .listing_parent_wrapper .dropdown-menu button {text-align: left;}
.lisiting_table .listing_parent_wrapper .dropdown-menu a i, .lisiting_table .listing_parent_wrapper .dropdown-menu button i {margin-right: 4px; width: 15px; padding-inline: 0;}


.listing_parent_wrapper .white-box:has(.not-found-message) .dataTables_wrapper { padding: 0;}
.not-found-message { padding-bottom: 28px; }


.toggle-container { display: flex;align-items: center;gap: 10px;}
.toggle-container .switch { position: relative; display: inline-block; width: 42px; height: 24px; margin-bottom: 0;}
.toggle-container .switch input { opacity: 0; width: 0; height: 0;}
.toggle-container .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #D8DAE5; border-radius: 34px; transition: 0.4s;}
.toggle-container .slider:before { content: ""; position: absolute; left: 4px;bottom: 4px; height: 16px; width: 16px; border-radius: 50%; background-color: #ebebeb; transition: 0.4s;}
.toggle-container input:checked + .slider { background-color: var(--primary-color); }
.toggle-container input:checked + .slider:before { transform: translateX(18px);}
.toggle-container:has(input:checked) .toggle-label.deactive, .toggle-container:not(:has(input:checked)) .toggle-label.active { display: none;}
.toggle-container:not(:has(input:checked)) .toggle-label.deactive, .toggle-container:has(input:checked) .toggle-label.active { display: block;}

.toggle-label { font-size: 12px; font-weight: 500; color: #000;}
/* input:checked ~ #toggleLabel { color: #4e73df; } */

.lisiting_table .listing_parent_wrapper .white-box .not-found-message:not( .not-found-message:last-child) { display: none; }

.noti_drop .fa-bell.dark-yellow { padding-top: 23px; }

.page-wrapper:has(.chart-sec) { min-height: 120vh !important;}

#autoSchedule .heading_switch_wrapper {margin-top: 15px; margin-bottom: 20px;}

tr:has(.dropdown-toggle[aria-expanded="true"]) td { border: 3px solid var(--primary-color); border-inline: 0;}
tr:has(.dropdown-toggle[aria-expanded="true"]) td:first-child { border-left: 3px solid var(--primary-color); border-top-left-radius: 15px; border-bottom-left-radius: 10px;}
tr:has(.dropdown-toggle[aria-expanded="true"]) td:last-child { border-right: 3px solid var(--primary-color); border-top-right-radius: 15px; border-bottom-right-radius: 15px;}

.filter_search input { box-shadow: none;  background-color: transparent;}
/* .booking_search .form-group.has-feedback.has-search { width: 615px; } */

.booking_search .form-group.has-feedback.has-search { width: 635px; margin-bottom: 0;}
.booking_search .form-group.has-feedback.has-search .input-daterange-datepicker {width: 166px;}

.booking_search .form-group.has-feedback.has-search .search_field_wrapper {position: relative; padding-right: 20px}
.booking_search .form-group.has-feedback.has-search .search_field_wrapper:before { content: ""; right: 5px; top: 0; bottom: 0; position: absolute; width: 1px; height: 75%; background-color: #e2e8f0; margin: auto 0;}

html[lang="es"] .booking_search .form-group.has-feedback.has-search {width: 715px;margin-bottom: 0;}
html[lang="es"] .booking_search .form-group.has-feedback.has-search .input-daterange-datepicker {width: 185px; flex-shrink: 0}

.review_index_main_sec .form-group.has-feedback.has-search { width: 500px; margin-bottom: 0;}
.review_index_main_sec .form-group.has-feedback.has-search .input-daterange-datepicker {width: 166px; flex-shrink: 0}

.review_index_main_sec .form-group.has-feedback.has-search .search_field_wrapper {position: relative; padding-right: 20px}
.review_index_main_sec .form-group.has-feedback.has-search .search_field_wrapper:before { content: ""; right: 5px; top: 0; bottom: 0; position: absolute; width: 1px; height: 75%; background-color: #e2e8f0; margin: auto 0;}

html[lang="es"] .review_index_main_sec .form-group.has-feedback.has-search {width: 480px;margin-bottom: 0;}
html[lang="es"] .review_index_main_sec .form-group.has-feedback.has-search .input-daterange-datepicker {width: 190px; flex-shrink: 0}

.review_index_main_sec #review-wrapper table tbody tr td.form_btn .dropdown-menu {width: 170px; translate: -120px 10px;}

body:not(:has(#drafts:checked)) .draft_text, .not_found { display: none; } /* Not Found text, draft text */
body:has(.no_booking, .loading) .csv_btn_all, body:has(.no_booking, .loading) .csv_btn  { pointer-events: none; opacity: 0.45; cursor: no-drop; } /* Booking CSV download when there is no booking */
.csv_btn_all:not(body:has([data-status=''].active) .csv_btn_all) { display: none;}

#helpCenter .mod_cust_text .main_heading { color: #000; }
#helpCenter .mod_cust_text .main_heading > span { color: #747474; }

i.yellow_icon { color: var(--primary-color) !important; }

#view_review_modal .review_comment p {font-size: 14px;}

#review-wrapper table tbody tr td:has(.review_profile_image) {text-align: center; width: 115px;}
#review-wrapper table tbody tr td .review_profile_image {width: 50px; height: 50px; object-fit: cover; object-position: center; border-radius: 50%;}


/* Language translation */

.panel-group .translation-panel.panel { margin-block: 20px; border: 1px solid var(--primary-color); border-radius: 15px; overflow: hidden;}
.panel-group .translation-panel .section-header { margin: 0; background: #fff;}
.panel-group .translation-panel .section-header:has([aria-expanded="true"]) { background: var(--primary-color);}
.panel-group .translation-panel.panel .panel-heading a[data-toggle=collapse]:before { display: none; }
.panel-group .translation-panel .panel-title a { padding: 6px 0; font-size: 15px; font-family: 'Poppins-SemiBold'; font-weight: 400;}

.panel-group .translation-panel .panel-title a:hover { background-color: transparent;}

.panel-group .translation-panel .panel-heading a i { color: var(--primary-color); transition: 0.3s ease-in;}
.panel-group .translation-panel .panel-heading a[aria-expanded="true"] i { color: #fff; rotate: 180deg; transition: 0.3s ease-in;}

.translation-table>tbody>tr>td { width: clamp(20%, 50%, 100%); }
.translation-table>tbody>tr>td>a { padding: 10px 20px; border-bottom-right-radius: 0; border-bottom-left-radius: 0; display: inline-block; width: 100%; border-inline: 1px solid transparent;     border-top: 1px solid transparent; transition: 0.3s ease-in;}
.translation-table>tbody>tr>td>a:hover { border-radius: 10px; border: 1px solid var(--primary-color); color: var(--primary-color); transition: 0.3s ease-in;}

.translation-table .editable-buttons .btn-primary, .translation-table .editable-buttons .btn-default { padding: 6px 20px; border-radius: 10px; }
.translation-table .editable-buttons .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); color: #000; }
.translation-table .editable-buttons .btn-default { background-color: #000; color: #fff;}

.translation-panel.panel .panel-heading a[aria-expanded="true"] i { color: #fff; rotate: 180deg; transition: 0.3s ease-in;}

.translation-table .editable-buttons .btn-primary, .translation-table .editable-buttons .btn-default { padding: 6px 20px; border-radius: 10px; }
.translation-table .editable-buttons .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); color: #000; }
.translation-table .editable-buttons .btn-default { background-color: #000; color: #fff;}

.translation-table .editableform .form-group > div { display: flex; align-items: center; gap: 10px;}
.translation-table .editableform .form-group > div .editable-input input { border-radius: 15px; height: 45px;  padding-left: 15px;  width: 100%;}
.translation-table .editable-container, .translation-table .form-group, .translation-table .editable-input { width: 100%;}
.translation-table .editable-container .editable-clear-x { right: 10px; }

.translation-table .language-flag img { border-radius: 3px; height: 17px; width: 25px;}

.globe .d-flex { display: flex;}
.dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover { background-color: #FFCE32; }

.table>tbody>tr>td, .table>tbody>tr>td .dataTables_empty {font-family: 'Poppins-SemiBold';font-size: 14px;line-height: 19.6px;text-align: center;}

.calendar_main_container #download_ics_btn {padding-block: 0; height: 54px; display: flex; justify-content: center; align-items: center;}

#amenities-table tr td.form_btn .dropdown-item {text-align: center; padding: 10px; text-transform: capitalize;}


#translation-accordion .translation-table tbody td:has(.editable-container) .editable.editable-open {display: none !important;}
#translation-accordion .translation-table tbody td:has(.editable-container) .editable-container .editable-input textarea {border-radius: 8px; border: 1px solid #ffce32; padding-bottom: 20px !important; height: fit-content; max-height: unset !important; overflow-y: visible !important; overflow: hidden !important}
#translation-accordion .translation-table tbody td:has(.editable-container) .editable-container .control-group > div {flex-direction: column; align-items: flex-start; padding-bottom: 20px; position: relative;}
#translation-accordion .translation-table tbody td:has(.editable-container) .editable-container .control-group > div .editable-buttons {position: absolute; right: 30px; bottom: 0; background: white; padding: 0 10px; display: flex; gap: 3px; align-items: center; margin-left: 0 !important}
#translation-accordion .translation-table tbody td:has(.editable-container) .editable-container .control-group > div .editable-buttons button {padding: 0; margin: 0; width: 35px; height: 35px; border: 0;}
#translation-accordion .translation-table tbody td:has(.editable-container) .editable-container .control-group > div .editable-buttons button i {padding: 0}

.lisiting_table .listing_parent_wrapper .listing_table tr th, .lisiting_table .listing_parent_wrapper .listing_table tr td {text-align: center;}

.service_provider_index_sec .notify-box .comment-widget {margin-top: 0; min-height: 208px;}
.service_provider_index_sec .notify-box .comment-widget .media-list li:last-child {padding-bottom: 0; border-bottom: 0; padding-top: 15px;}
.service_provider_index_sec .notify-box .comment-widget .media .media-body a {overflow: hidden; display: -webkit-box; -webkit-line-clamp: 1; line-clamp: 1; -webkit-box-orient: vertical;}

.service_provider_index_sec .main_cart_sec .card_element .card_parent {height: 96.5px;}

/* Listing index page add listing button style fixes starts from here */

.listing_index .custom_btns .head_bt a {height: 50px; display: flex; justify-content: center; align-items: center; padding-block: 0}
.listing_index .custom_btns .head_bt .view_change {height: 50px;}

/* Listing index page add listing button style fixes ends here */

html {overflow: hidden;}
#wrapper .sidebar {height: 100vh;}
#wrapper .page-wrapper {min-height: calc(100vh - 80px); height: calc(100vh - 80px); overflow-y: auto; padding-bottom: 30px}
#wrapper .page-wrapper footer {position: relative; left: unset; margin-top: 30px;}