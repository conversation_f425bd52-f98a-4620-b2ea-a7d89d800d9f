<div class="col-md-12 listing_custom_meta divider listing_data">
    <ul class="list-unstyled d-flex gap-3 m-0 parent-box">
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/user.svg') }}" alt="" height="20px" width="20px">
            {{ $listing->detail->guests ?? '0' }} {{ translate('listing_details.guests') }}
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/bedroom.svg') }}" alt="" height="20px" width="20px">
            {{ $listing->detail->bedrooms ?? '0' }} {{ translate('listing_details.bedrooms') }}
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/bath.svg') }}" alt="" height="20px" width="20px">
            {{ $listing->detail->bathrooms ?? '0' }} {{ translate('listing_details.bathrooms') }}
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/check-in.png') }}" alt="" height="20px" width="20px">
            {{ translate('listing_details.check_in_after') }} {{ $listing->detail->check_in_time ?? '0' }}
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/check-out.png') }}" alt="" height="20px" width="20px">
            {{ translate('listing_details.check_out_before') }} {{ $listing->detail->check_out_time ?? '0' }}
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
            @if (isset($listing->detail->pet) && $listing->detail->pet == 'yes')
                {{ translate('listing_details.pets_allowed') }}
            @else
                {{ translate('listing_details.pets_not_allowed') }}
            @endif
        </li>
    </ul>
</div>

{{-- Amenities --}}
@if (isset($listing->amenity_detail[0]))
    <div class="col-lg-12 divider listing_data listing_amenities">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.everything_included_in_this_place') }}</h3>
            <div class="parent-box row g-0 align-items-center">
                @foreach ($listing->amenity_detail as $key => $amenities)
                    @if ($key < 18)
                        <div class="col-md-3 col-sm-12">
                            <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                                <img src="{{ asset('website') . '/' . $amenities->image }}" alt=""
                                    height="30px" width="30px">
                                <div class="amenity-data">
                                    <span>{{ ucfirst($amenities->name) ?? '' }}</span>
                                    @isset($amenities->description)
                                        <p class="amenties_desc fs-14 text-black-50">
                                            {{ $amenities->description ?? '' }}
                                        </p>
                                    @endisset

                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
                {{-- @foreach ($listing->custom_amenities as $custom_amenities)
                    <div class="col-md-3">
                        <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                            <img src="{{ asset('website/images/bath.svg') }}" alt="" height="30px"
                                width="30px">
                            <div class="amenity-data">
                                <span>{{ $custom_amenities->name }}</span>
                                <p class="amenties_desc fs-14 text-black-50">{!! $custom_amenities->description !!}</p>
                            </div>
                        </div>
                    </div>
                @endforeach --}}
            </div>
            {{-- @if (count($listing->amenity_detail) > 16) --}}
            <button type="button" class="button button1 mt-3" data-bs-toggle="modal" data-bs-target="#all-amenties">
                {{ translate('listing_details.show_all') }} {{ count($listing->amenity_detail) }} {{ translate('see_all_expansion.accommodation_view_btn') }}
            </button>
            {{-- @endif --}}
        </div>
    </div>


    <div class="modal fade all-amenties" id="all-amenties" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content pb-0">
                <div class="modal-header border-0 ">
                    <h4 class="modal-title mx-auto">{{ translate('listing_details.everything_included_in_this_place') }}</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pb-lg-10 px-lg-10 pt-4 pb-0">
                    <div class="parent-box row g-0 align-items-start">
                        @foreach ($listing->amenity_detail as $amenities)
                            <div class="col-md-12 divider pb-3">
                                <div class="box d-flex gap-3 align-items-center" data-aos="fade">
                                    <img src="{{ asset('website') . '/' . $amenities->image }}" alt=""
                                        height="30px" width="30px">
                                    <div class="amenity-data ">
                                        <span>{{ translateDynamic($amenities->name) ?? '' }}</span>
                                        @isset($amenities->translations->firstWhere('locale', session('locale', 'en'))->description)
                                            <p class="amenties_desc fs-14 text-black-50">
                                                {{ $amenities->translations->firstWhere('locale', session('locale', 'en'))->description ?? '' }}
                                            </p>
                                        @endisset

                                    </div>
                                </div>
                            </div>
                        @endforeach
                        {{-- @foreach ($listing->custom_amenities as $custom_amenities)
                            <div class="col-md-12 divider pb-3">
                                <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                                    <img src="{{ asset('website/images/bath.svg') }}" alt="">
                                    <div class="amenity-data ">
                                        <span>{{ $custom_amenities->name }}</span>
                                        <p class="amenties_desc fs-14 text-black-50">{!! $custom_amenities->description !!}</p>

                                    </div>
                                </div>
                            </div>
                        @endforeach --}}
                    </div>
                </div>
                {{-- <div class="modal-footer border-0">
                    <button class="btn button login btn-block mb-4 action-button" data-bs-dismiss="modal">Cancel</button>
                </div> --}}
            </div>
        </div>
    </div>
@endif
{{-- End Amenities --}}

{{-- Key and feature --}}
@if (isset($listing->key_features[0]))
    <div class="col-lg-12 divider listing_data listing_key_feature">
        <div class="key_features">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.key_features') }}</h3>
            <div class="parent-feature parent-box row g-0 gap-3 align-items-start">
                @foreach ($listing->key_features as $key_feature)
                    <div class="box col-md-3" data-aos="fade">
                        <h6 class="fs-16 semi-bold">{{ translateDynamic($key_feature->title) ?? '' }}</h6>
                        <p class="fs-12">{!! translateDynamic($key_feature->description) !!}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif
{{-- End Key and feature --}}
