@extends('website.layout.master')
@push('css')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bs-stepper/dist/css/bs-stepper.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css">
    <style>
        [tooltip] {
            position: relative;
        }

        [tooltip]::before,
        [tooltip]::after {
            text-transform: none;
            font-size: .9em;
            line-height: 1;
            user-select: none;
            pointer-events: none;
            position: absolute;
            display: none;
            opacity: 0;
        }

        [tooltip]::before {
            content: '';
            border: 5px solid transparent;
            z-index: 1001;
        }

        [tooltip]::after {
            content: attr(tooltip);
            font-family: Helvetica, sans-serif;
            text-align: center;
            min-width: 15em;
            max-width: 21em;
            /* white-space: nowrap; */
            overflow: hidden;
            /* text-overflow: ellipsis; */
            padding: 1ch 1.5ch;
            border-radius: .3ch;
            box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35);
            background: #333;
            color: #fff;
            z-index: 1000;
        }

        [tooltip]:hover::before,
        [tooltip]:hover::after {
            display: block;
        }

        [tooltip='']::before,
        [tooltip='']::after {
            display: none !important;
        }

        [tooltip]:not([flow])::before,
        [tooltip][flow^="up"]::before {
            bottom: 100%;
            border-bottom-width: 0;
            border-top-color: #333;
        }

        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::after {
            bottom: calc(100% + 5px);
        }

        [tooltip]:not([flow])::before,
        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::before,
        [tooltip][flow^="up"]::after {
            left: 50%;
            transform: translate(-50%, -.5em);
        }

        [tooltip][flow^="down"]::before {
            top: 100%;
            border-top-width: 0;
            border-bottom-color: #333;
        }

        [tooltip][flow^="down"]::after {
            top: calc(100% + 5px);
        }

        [tooltip][flow^="down"]::before,
        [tooltip][flow^="down"]::after {
            left: 50%;
            transform: translate(-50%, .5em);
        }

        [tooltip][flow^="left"]::before {
            top: 50%;
            border-right-width: 0;
            border-left-color: #333;
            left: calc(0em - 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="left"]::after {
            top: 50%;
            right: calc(100% + 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="right"]::before {
            top: 50%;
            border-left-width: 0;
            border-right-color: #333;
            right: calc(0em - 5px);
            transform: translate(.5em, -50%);
        }

        [tooltip][flow^="right"]::after {
            top: 50%;
            left: calc(100% + 5px);
            transform: translate(.5em, -50%);
        }

        @keyframes tooltips-vert {
            to {
                opacity: .9;
                transform: translate(-50%, 0);
            }
        }

        @keyframes tooltips-horz {
            to {
                opacity: .9;
                transform: translate(0, -50%);
            }
        }

        [tooltip]:not([flow]):hover::before,
        [tooltip]:not([flow]):hover::after,
        [tooltip][flow^="up"]:hover::before,
        [tooltip][flow^="up"]:hover::after,
        [tooltip][flow^="down"]:hover::before,
        [tooltip][flow^="down"]:hover::after {
            animation: tooltips-vert 300ms ease-out forwards;
        }

        [tooltip][flow^="left"]:hover::before,
        [tooltip][flow^="left"]:hover::after,
        [tooltip][flow^="right"]:hover::before,
        [tooltip][flow^="right"]:hover::after {
            animation: tooltips-horz 300ms ease-out forwards;
        }


        /* Inline styles to ensure they're applied */
        #delete-account-stepper .bs-stepper-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            justify-content: center;
        }

        #delete-account-stepper .line {
            flex: 1;
            height: 2px;
            background-color: #e9ecef;
            margin: 0 1rem;
        }

        #delete-account-stepper .bs-stepper-circle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #6c757d;
            color: #fff;
            font-weight: 500;
        }

        #delete-account-stepper .active .bs-stepper-circle {
            background-color: var(--btn-yellow, #FFCE32);
        }

        #delete-account-stepper .bs-stepper-label {
            display: inline-block;
            margin: 0.25rem;
            font-weight: 500;
            font-size: 12px;
        }

        #delete-account-stepper .bs-stepper-content {
            margin-top: 20px;
        }

        #delete-account-stepper .content {
            display: none;
            padding: 0;
        }

        #delete-account-stepper .content.active {
            display: block;
        }

        #deleteAccountModal button#verify-password-btn:hover {
            color: var(--black);
            background-color: transparent;
            border: 1px solid var(--btn-yellow);
            transition: 0.55s;
        }

        #deleteAccountModal button#verify-password-btn {
            width: 100%;
            background-color: var(--btn-yellow);
            color: var(--black);
            border-color: var(--btn-yellow);
            border-radius: 12px;
            height: 42.6px;
        }

        body:has(#password-part.active) .step[data-target="#password-part"] .bs-stepper-circle {
            background-color: #ffce32;
        }

        body:has(#otp-part.active) .step[data-target="#otp-part"] .bs-stepper-circle {
            background-color: #ffce32;
        }

        body:has(#confirm-part.active) .step[data-target="#otp-part"] .bs-stepper-circle {
            background-color: #ffce32;
        }

        body:has(#confirm-part.active) .step[data-target="#confirm-part"] .bs-stepper-circle {
            background-color: #ffce32;
        }

        .btn_yellow {
            width: 100%;
            background-color: var(--btn-yellow);
            color: var(--black);
            border-color: var(--btn-yellow);
            border-radius: 12px;
            height: 43px;
        }

        /* Country flag styles */
        .flag-icon {
            width: 20px;
            height: 15px;
            margin-right: 8px;
            display: inline-block;
            background-size: cover;
            background-position: center;
            border-radius: 2px;
        }

        .country-select-wrapper {
            position: relative;
        }

        #country-select {
            padding-left: 40px;
            background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 12px;
            padding-right: 40px;
        }

        .selected-flag {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            z-index: 2;
        }

        .country-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
        }

        .country-option:hover {
            background-color: #f8f9fa;
        }
    </style>
    <style>
        /* Inline styles for stepper */
        #verification-stepper .bs-stepper-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        #verification-stepper .line {
            flex: 1;
            height: 2px;
            background-color: transparent;
            margin: 0 1rem;
            border: 2px dashed #FFD324;
        }

        #verification-stepper .bs-stepper-circle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #CECECE;
            color: #4A4A4A;
            font-weight: 500;
        }

        #verification-stepper .active .bs-stepper-circle {
            background-color: var(--btn-yellow, #FFCE32);
            color: #4A4A4A;
        }

        #verification-stepper .bs-stepper-label {
            display: inline-block;
            margin: 0.25rem;
            font-weight: 500;
            color: #4A4A4A;
        }

        #verification-stepper .content {
            display: none;
            padding: 1.5rem 0;
        }

        #verification-stepper .content.active {
            display: block;
        }
    </style>
@endpush
@section('content')
    <section class="det_form setting_page">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h4>{{ translate('user_account_setting.account_settings') }}</h4>
                    <p>{{ translate('user_account_setting.get_started') }}</p>
                    <div class="row">
                        {{-- <form action="{{ route('userprofile.profile_img') }}" method="POST" enctype="multipart/form-data"> --}}
                        <form action="" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="col-md-12">
                                <div class="profile_img_tooltip_wrapper d-flex gap-3 align-items-center mt-4">
                                    <div class="profile_picture">
                                        <div class="profile-image-div m-0">
                                            <img id="blah" src="{{ asset('website') . '/' . auth()->user()->avatar }}"
                                                alt="{{ translate('user_account_setting.profile_picture') }}" />
                                            <input type='file' name="profile_img" id="profile_img" />
                                            <label for="profile_img" class="profile_img_edit pencil_icon"><i
                                                    class="fas fa-pencil-alt"></i></label>
                                        </div>
                                        @error('profile_img')
                                            <p class="text-danger mb-0">{{ $message }}</p>
                                        @enderror
                                        {{-- <div class="form_field_padding setting_btn ms-3">
                                            <input class="btn setting_btn" style="padding-block: 10px;padding-inline: 63px;"
                                                type="submit" value="{{ translate('user_account_setting.update') }}">
                                        </div> --}}
                                    </div>
                                    <div class="profile_tooltip">
                                        <label tooltip="{{ translate('user_account_setting.profile_picture_tooltip') }}"
                                            for=""><i class="fas fa-info-circle"></i></label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="accordion profile_setting" id="accordionExample">
                        <div class="accordion-item wh-box mt-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                    {{ translate('user_account_setting.personal_information') }}
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <div
                                        class="d-flex gap-2 align-items-center justify-content-end profile_setting_top_buttons_wrapper">
                                        <button
                                            class="btn btn-danger delete_account_btn">{{ translate('user_account_setting.delete_account') }}</button>
                                        <div class="btn-row ">
                                            <button type="button" class="btn m-0" href="#" data-bs-toggle="modal"
                                                data-bs-target="#change">{{ translate('user_account_setting.change_password') }}</button>
                                        </div>
                                        <a href="javascript:void(0)" class="edit-sec pencil_icon">
                                            <i class="fas fa-pencil-alt"></i>
                                            {{-- <img  src="{{ asset('website') }}/images/edit.png" alt="your image" /> --}}
                                        </a>
                                    </div>
                                    <form id="profileForm" action="{{ route('userprofile.update') }}" method="POST">
                                        @csrf
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form_field_padding">
                                                    <input type="text" name="first_name"
                                                        value="{{ auth()->user()->first_name ?? '' }}"
                                                        class="form-control {{ auth()->user()->hasRole('user') ? '' : 'disabled_toggle' }}  @error('first_name') is-invalid @enderror"
                                                        placeholder="{{ translate('user_account_setting.first_name') }}"
                                                        disabled>
                                                    @error('first_name')
                                                        <p class="text-danger mt-1">{{ $message }}</p>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form_field_padding">
                                                    <input type="text" name="last_name"
                                                        value="{{ auth()->user()->last_name ?? '' }}"
                                                        class="form-control {{ auth()->user()->hasRole('user') ? '' : 'disabled_toggle' }}  @error('last_name') is-invalid @enderror"
                                                        placeholder="{{ translate('user_account_setting.last_name') }}"
                                                        disabled>
                                                    @error('last_name')
                                                        <p class="text-danger mt-1">{{ $message }}</p>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form_field_padding">
                                                    <input type="hidden" name="phone" class="country_code_merged"
                                                        value="">
                                                    <input type="tel" name=""
                                                        value="{{ auth()->user()->phone ?? '' }}" id="telephone_profile"
                                                        class="phone_default form-control country_code_profile disabled @error('phone') is-invalid @enderror"
                                                        placeholder="{{ translate('user_account_setting.phone') }}"
                                                        pattern="^\d+$">
                                                    @error('phone')
                                                        <p class="text-danger mt-1">{{ $message }}</p>
                                                    @enderror
                                                    <p class="text-danger" id="confirm_phone_error"></p>
                                                </div>
                                            </div>
                                            {{-- <div class="col-md-6 d-flex gap-2 align-items-center box_head"> --}} {{-- Before removing edit email button (FOR BACKUP) --}}
                                            <div class="col-md-6">
                                                <div class="form_field_padding">
                                                    <input type="email" name="email"
                                                        value="{{ auth()->user()->email ?? '' }}"
                                                        class="form-control email_default @error('email') is-invalid @enderror"
                                                        placeholder="{{ translate('user_account_setting.email') }}"
                                                        disabled data-email="{{ auth()->user()->email ?? '' }}">
                                                    @error('email')
                                                        <p class="text-danger mt-1">{{ $message }}</p>
                                                    @enderror
                                                    <p class="text-danger" id="confirm_email_error"></p>
                                                </div>
                                                {{-- <a href="" data-bs-toggle="modal" data-bs-target="#change_email"
                                                    class="edit">
                                                    <img src="{{ asset('website') }}/images/edit.png" alt="your image" />
                                                </a> --}}
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form_field_padding">
                                                    <div class="country-select-wrapper position-relative">
                                                        {{-- <select name="country" id="country-select"  class="form-control  @error('country') is-invalid @enderror">
                                                            <option value="">{{ translate('user_account_setting.select_country') }}</option>
                                                            @foreach ($countries as $country)
                                                                <option value="{{ $country->name }}"
                                                                    data-flag="{{ $country->flags }}"
                                                                    {{ (auth()->user()->profile->country ?? '') == $country->name ? 'selected' : '' }}>
                                                                    {{ $country->name }}
                                                                </option>
                                                            @endforeach
                                                        </select> --}}

                                                        <select class="form-control @error('country') is-invalid @enderror"
                                                            id="country-select" name="country">
                                                            <option value="" disabled selected>
                                                                {{ translate('user_account_setting.select_country') }}
                                                            </option>
                                                            @foreach ($countries as $country)
                                                                <option @if (auth()->user()->identity_verified == 'verified') disabled @endif
                                                                    value="{{ $country->sortname }}"
                                                                    data-flag="{{ $country->flags }}"
                                                                    {{ (auth()->user()->profile->country ?? '') == $country->sortname ? 'selected' : '' }}>
                                                                    {{ $country->name }}
                                                                </option>
                                                            @endforeach
                                                        </select>

                                                        {{-- <span class="selected-flag position-absolute" id="selected-flag" style="left: 12px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                                                            @if (auth()->user()->profile->country ?? '')
                                                                @php
                                                                    $selectedCountry = $countries->where('name', auth()->user()->profile->country)->first();
                                                                @endphp
                                                                @if ($selectedCountry)
                                                                    <span class="flag-icon flag-icon-{{ $selectedCountry->flags }}"></span>
                                                                @endif
                                                            @endif
                                                        </span> --}}
                                                    </div>
                                                    @error('country')
                                                        <p class="text-danger mt-1">{{ $message }}</p>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form_field_padding">
                                                    <input class="btn setting_btn" id="profile_update" type="submit"
                                                        value="{{ translate('user_account_setting.save_changes') }}">
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item wh-box mt-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    {{ translate('user_account_setting.session_history') }}
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse session_history_accordion" data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    {{-- <th>Session ID</th> --}}
                                                    <th>{{ translate('user_account_setting.start_time') }}</th>
                                                    <th>{{ translate('user_account_setting.end_time') }}</th>
                                                    <th>{{ translate('user_account_setting.duration') }}</th>
                                                    <th>{{ translate('user_account_setting.ip_address') }}</th>
                                                    <th>{{ translate('user_account_setting.location') }}</th>
                                                    <th>{{ translate('user_account_setting.device') }}</th>
                                                    <th>{{ translate('user_account_setting.browser') }}</th>
                                                    <th>{{ translate('user_account_setting.platform') }}</th>
                                                    <th>{{ translate('user_account_setting.status') }}</th>
                                                    <th>{{ translate('user_account_setting.action') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($user_sessions as $session)
                                                    <tr style="vertical-align: middle">
                                                        {{-- <td>{{ $session->session_id }}</td> --}}

                                                        {{-- <td>{{ $session->login_time ? $session->login_time->format('d M Y H:i:s') : '-' }}</td> --}}
                                                        <td>
                                                            {{ formatLocalizedDateTime($session->login_time ? $session->login_time->format('d M Y H:i:s') : '-') }}
                                                        </td>

                                                        <td>
                                                            {{ formatLocalizedDateTime($session->logout_time ? $session->logout_time->format('d M Y H:i:s') : '-') }}
                                                        </td>

                                                        <td>{{ $session->logout_time ? $session->logout_time->diffForHumans($session->login_time, true) : '-' }}</td>
                                                        <td>{{ $session->ip_address }}</td>
                                                        <td>{{ $session->location }}</td>
                                                        <td>
                                                            {{-- {{ translateDynamic(ucfirst($session->device_type)) ?? '' }} --}}
                                                            @if (($session->device_type ?? '') == 'desktop')
                                                                {{ translate('user_account_setting.device_desktop') }}
                                                            @else
                                                                {{ translate('user_account_setting.device_mobile') }}
                                                            @endif
                                                        </td>
                                                        <td>{{ $session->browser }}</td>
                                                        <td>{{ $session->platform }}</td>
                                                        <td class="fw-bolder">
                                                            @if ($session->is_active)
                                                                <span
                                                                    class="text-success">{{ translate('user_account_setting.active') }}</span>
                                                            @else
                                                                <span
                                                                    class="text-secondary">{{ translate('user_account_setting.inactive') }}</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @if ($session->is_active)
                                                                @if ($session->session_id === session()->getId())
                                                                    <span
                                                                        class="default_badge badge fw-bold">{{ translate('user_account_setting.current_session') }}</span>
                                                                @else
                                                                    <button type="button" class="btn btn-sm btn-danger"
                                                                        onclick="logoutSession('{{ $session->session_id }}')">
                                                                        {{ translate('user_account_setting.logout') }}
                                                                    </button>
                                                                @endif
                                                            @else
                                                                <span class="text-muted">-</span>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item wh-box mt-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    {{ translate('user_account_setting.payment_methods') }}
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse"
                                data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <div class="row pt-1">
                                        <div class="col-md-12">
                                            {{ translate('user_account_setting.add_manage_payment_methods') }}
                                        </div>
                                        <div class="col-md-7">
                                            {{-- <div class="box_head">
                                                <h4>Cards</h4>
                                            </div> --}}
                                            <div class="card_wrapper">
                                                @foreach ($cards['records'] ?? [] as $card)
                                                    <div
                                                        class="card_parent d-flex justify-content-between align-items-center divider py-3">
                                                        <div
                                                            class="d-flex justify-content-between w-90 align-items-center">
                                                            <div class="d-flex gap-3 align-items-center">
                                                                @php
                                                                    $cardNumber = $card['fields']['card_number'] ?? '';
                                                                    $maskedCardNumber = str_replace(
                                                                        'X',
                                                                        '•',
                                                                        $cardNumber,
                                                                    );
                                                                    $cardType = $card['fields']['card_type'] ?? '';
                                                                    $cardTypeImg = strtolower(
                                                                        $card['fields']['card_type'] ?? '',
                                                                    );
                                                                @endphp
                                                                <div class="card_type">
                                                                    <div class="cards-img">
                                                                        @if ($cardTypeImg == 'visa')
                                                                            <img src="{{ asset('website/images/visa_new.png') }}"
                                                                                alt="Visa" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @elseif ($cardTypeImg == 'mastercard')
                                                                            <img src="{{ asset('website/images/master_card_new.png') }}"
                                                                                alt="Mastercard" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @elseif ($cardTypeImg == 'discover')
                                                                            <img src="{{ asset('website/images/discover_new.png') }}"
                                                                                alt="Discover" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @elseif ($cardTypeImg == 'american express')
                                                                            <img src="{{ asset('website/images/american_express_new.png') }}"
                                                                                alt="American" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @elseif ($cardTypeImg == 'jcb')
                                                                            <img src="{{ asset('website/images/JCB_new.png') }}"
                                                                                alt="JCB" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @elseif ($cardTypeImg == 'diners club')
                                                                            <img src="{{ asset('website/images/diners_club_new.png') }}"
                                                                                alt="Diners Club" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @elseif ($cardTypeImg == 'unionpay')
                                                                            <img src="{{ asset('website/images/union_pay_new.png') }}"
                                                                                alt="Union Pay" height="40px"
                                                                                width="40px" class="img-fluid">
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                                <div class="card_info">
                                                                    <div class="card_num">
                                                                        {{ $cardType }}
                                                                        {{ $maskedCardNumber }}
                                                                    </div>
                                                                    <div class="card_exp">
                                                                        {{ translate('user_account_setting.expiration') }}
                                                                        {{ $card['fields']['expiry_date'] ?? '' }}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @if (($card['fields']['is_default'] ?? '') == 1)
                                                                <div class="default_badge badge">
                                                                    {{ translate('user_account_setting.default') }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                        <div class="dropdown">
                                                            <button class="btn btn_trans" type="button"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <div class="dropdown-menu">
                                                                <div class="dropdown-item">
                                                                    @if (($card['fields']['is_default'] ?? '') != 1)
                                                                        <button class="btn btn_trans deafult_btn p-0"
                                                                            onclick="location.href = `{{ route('cards.default', $card['fields']['skyflow_id']) }}`">{{ translate('user_account_setting.set_as_default') }}</button>
                                                                    @endif
                                                                </div>
                                                                <div class="dropdown-item">
                                                                    <button class="btn btn_trans remove_btn p-0"
                                                                        onclick="location.href = `{{ route('cards.delete', $card['fields']['skyflow_id']) }}`">{{ translate('user_account_setting.remove') }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn setting_btn button button1 mt-3" data-bs-toggle="modal"
                                        data-bs-target="#add_card">{{ translate('user_account_setting.add_payment_method') }}</button>
                                    {{-- Remove this when dynamic done --}}
                                    <table class="table cards_table d-none">
                                        <thead>
                                            <tr>
                                                {{-- <th>ID</th> --}}
                                                <th>{{ translate('user_account_setting.card_number') }}</th>
                                                {{-- <th>Card Holder Name</th>
                                                <th>Expiry Date</th> --}}
                                                {{-- <th>CVC</th> --}}
                                                {{-- <th></th> --}}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($cards["records"] ?? [] as $card)
                                                <tr>
                                                    <td>{{ $card['fields']['card_number'] ?? '' }}</td>
                                                    <td>
                                                    </td>
                                                    {{-- <td>{{ $card['fields']['cardholder_name'] ?? '' }}</td>
                                                    <td>{{ $card['fields']['expiry_date'] ?? '' }}</td> --}}
                                                    {{-- <td>
                                                        <div class="row">
                                                            <input type="number" class="col-md-3 form-control">
                                                        </div>
                                                        {{ decrypt($card->cvc) }}
                                                    </td> --}}
                                                    {{-- <td>
                                                        @if (!$card->is_default)
                                                            <button class="btn btn-secondary"
                                                                onclick="location.href = `{{ route('default_card', $card->id) }}`"><i
                                                                    class="fas fa-check"></i></button>
                                                        @endif
                                                        <button class="btn btn-danger"
                                                            onclick="location.href = `{{ route('delete_card', $card->id) }}`"><i
                                                                class="fas fa-trash"></i></button>
                                                    </td> --}}
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="8">
                                                        <h6 class="text-center">
                                                            {{ translate('user_account_setting.no_saved_cards') }}</h6>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec-2-profileSetting verification_webaccount_setting_sec pb-3" id="verfication-kyc">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div
                        class="img d-flex justify-content-sm-between justify-content-center align-items-center gap-sm-0 gap-2">
                        <h3 class="semi-bold text-shadow">
                            <span class="white d-block fs-66 pb-3 bold ">LuxuStars</span>
                            <span class="text-uppercase">{{ translate('user_account_setting.identity_verification') }}</span>
                        </h3>
                        @if (auth()->user()->identity_verified == 'verified')
                            <img src="{{ asset('website/images/check.png') }}" height="135" alt="">
                        @else
                            @if (!auth()->user()->phone_verified_at)
                                {{-- <a href="javascript:void(0)" class="button" onclick="openVerificationStepper()">Phone verification required</a> --}}
                                <a href="javascript:void(0)" class="button" onclick="openVerificationStepper()">Verify
                                    {{ translate('user_account_setting.now') }}</a>
                            @else
                                <a href="javascript:void(0)" class="button" onclick="openVerificationStepper()">Verify
                                    {{ translate('user_account_setting.now') }}</a>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Delete Account Modal -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content p-4">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteAccountModalLabel">{{ translate('user_account_setting.delete_account') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Stepper -->
                    <div class="bs-stepper" id="delete-account-stepper">
                        <div class="bs-stepper-header">
                            <div class="step active" data-target="#password-part">
                                <button type="button" class="step-trigger">
                                    <span class="bs-stepper-circle">1</span>
                                    <span
                                        class="bs-stepper-label">{{ translate('user_account_setting.verify_password') }}</span>
                                </button>
                            </div>
                            <div class="line"></div>
                            <div class="step" data-target="#otp-part">
                                <button type="button" class="step-trigger">
                                    <span class="bs-stepper-circle">2</span>
                                    <span
                                        class="bs-stepper-label">{{ translate('user_account_setting.verify_otp') }}</span>
                                </button>
                            </div>
                            <div class="line"></div>
                            <div class="step" data-target="#confirm-part">
                                <button type="button" class="step-trigger">
                                    <span class="bs-stepper-circle">3</span>
                                    <span class="bs-stepper-label">{{ translate('user_account_setting.confirm') }}</span>
                                </button>
                            </div>
                        </div>
                        <div class="bs-stepper-content p-0">
                            <!-- Password verification step -->
                            <div id="password-part" class="content active">
                                <div class="form-group mb-3 position-relative">
                                    <label for="current_password"
                                        class="form-label">{{ translate('user_account_setting.current_password') }}</label>
                                    <input type="password" class="form-control" id="current_password"
                                        name="current_password">
                                    <span toggle="#current_password" class="fa fa-fw fa-eye toggle-password"></span>
                                    <div class="invalid-feedback" id="password-error"></div>
                                </div>
                                <button class="btn btn_yellow"
                                    id="verify-password-btn">{{ translate('user_account_setting.next') }}</button>
                            </div>

                            <!-- OTP verification step -->
                            <div id="otp-part" class="content">
                                <div id="otp_display_container" class="alert alert-info mb-3 d-none">
                                    <p>{{ translate('user_account_setting.otp_code') }}: <strong
                                            id="otp_display"></strong></p>
                                    <p class="mb-0">{{ translate('user_account_setting.verify_otp_message') }}</p>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="otp_code"
                                        class="form-label">{{ translate('user_account_setting.enter_otp') }}</label>
                                    <input type="text" class="form-control" id="otp_code" name="otp_code">
                                    <div class="invalid-feedback" id="otp-error"></div>
                                </div>
                                <button class="btn btn_yellow"
                                    id="verify-otp-btn">{{ translate('user_account_setting.next') }}</button>
                            </div>

                            <!-- Final confirmation step -->
                            <div id="confirm-part" class="content">
                                <div class="alert alert-warning">
                                    <p>{{ translate('user_account_setting.are_you_sure_delete_account') }}</p>
                                    <ul>
                                        <li>{{ translate('user_account_setting.all_your_personal_information_will_be_anonymized') }}
                                        </li>
                                        <li>{{ translate('user_account_setting.you_will_not_be_able_to_log_in_again_with_this_account') }}
                                        </li>
                                        <li>{{ translate('user_account_setting.your_bookings_history_will_be_maintained_for_record_purposes') }}
                                        </li>
                                    </ul>
                                </div>
                                <button class="btn btn-danger"
                                    id="confirm-delete-btn">{{ translate('user_account_setting.delete_my_account') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Verification Stepper Modal -->
    <div class="modal fade" id="verificationStepperModal" tabindex="-1" aria-labelledby="verificationStepperModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title notranslate" id="verificationStepperModalLabel">
                        {{ translate('user_account_setting.complete_verification') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Stepper -->
                    <div class="bs-stepper" id="verification-stepper">
                        <div class="bs-stepper-header">
                            <div class="step verification_step {{ auth()->user()->phone_verified_at ? 'verified' : '' }}"
                                data-target="#phone-verification-part">
                                <button type="button" class="step-trigger">
                                    <span class="bs-stepper-circle">01</span>
                                    <span
                                        class="bs-stepper-label notranslate">{{ translate('user_account_setting.phone_verification') }}</span>
                                </button>
                            </div>
                            <div class="line"></div>
                            <div class="step" data-target="#kyc-verification-part">
                                <button type="button" class="step-trigger">
                                    <span class="bs-stepper-circle">02</span>
                                    <span
                                        class="bs-stepper-label notranslate">{{ translate('user_account_setting.identity_verification') }}</span>
                                </button>
                            </div>
                        </div>
                        <div class="bs-stepper-content">
                            <!-- Phone verification step -->
                            <div id="phone-verification-part" class="content">
                                <div class="text-center mb-4 phone_verification_container">

                                    <div class="main_image_wrapper">
                                        <img class="img-fluid" src="{{ asset('website/images/kyc_phone_verification_main.png') }}" alt="">
                                    </div>

                                    <h5 class="notranslate">
                                        {{ translate('user_account_setting.verify_your_phone_number') }}</h5>
                                    <p>
                                        {{ translate('user_account_setting.well_send_verification_code_to_your_phone_number') }}: <span class="phone_number">{{ auth()->user()->phone }}</span>
                                    </p>
                                    {{-- <p class="fw-bold">{{ auth()->user()->phone }}</p> --}}
                                    <p class="text-muted different_phone_number_text">
                                        {{ translate('user_account_setting.different_phone_number') }}?
                                        <a href="javascript:void(0)" id="change_phone_link"
                                            class="notranslate">
                                            {{ translate('user_account_setting.click_here_to_change') }}
                                        </a>
                                    </p>
                                </div>

                                <div id="phone-verification-form-container">
                                    <div id="phone-verification-otp-container" class="d-none">
                                        <div class="form-group mb-3">
                                            <label for="stepper_phone_verification_otp notranslate"
                                                class="form-label notranslate">
                                                {{ translate('user_account_setting.enter_verification_code') }}
                                            </label>
                                            <input type="text" class="form-control"
                                                id="stepper_phone_verification_otp">
                                            <div class="text-danger" id="stepper_phone_verification_error"></div>
                                        </div>
                                        <div class="mb-3 text-center">
                                            {{-- <label class="form-label">Choose verification method:</label> --}}
                                            <div class="text-center radio_input_wrapper">
                                                <label class="me-3" for="stepper_otp_type_sms">
                                                    <input type="radio" name="otp_type" id="stepper_otp_type_sms" value="sms" checked>
                                                    SMS
                                                </label>
                                                <label for="stepper_otp_type_whatsapp">
                                                    <input type="radio" name="otp_type" id="stepper_otp_type_whatsapp" value="whatsapp">
                                                    WhatsApp
                                                </label>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-start align-items-center mb-3">
                                            <span id="stepper_otp_timer" class="text-muted"></span>
                                            <button class="notranslate btn btn-link p-0 d-none"
                                                id="stepper_resend_otp_button">
                                                {{ translate('user_account_setting.resend_code') }}
                                            </button>
                                        </div>
                                        <button class="notranslate btn button button1 w-100"
                                            id="stepper_verify_phone_button">
                                            {{ translate('user_account_setting.verify') }}</button>
                                    </div>

                                    <div id="phone-verification-send-container">
                                        <div class="mb-3 text-center">
                                            {{-- <label class="form-label">Choose verification method:</label> --}}
                                            <div class="text-center radio_input_wrapper">
                                                <label class="me-3" for="stepper_otp_send_sms">
                                                    <input type="radio" id="stepper_otp_send_sms" name="stepper_otp_type" value="sms" checked>
                                                    SMS
                                                </label>
                                                <label for="stepper_otp_send_whatsapp">
                                                    <input type="radio" id="stepper_otp_send_whatsapp" name="stepper_otp_type" value="whatsapp">
                                                    WhatsApp
                                                </label>
                                            </div>
                                            <div class="disclaimer_wrapper">
                                                <label for="disclaimer_check">
                                                    <input type="checkbox" name="disclaimer" id="disclaimer_check">
                                                    By clicking this checkbox and providing your phone number, you agree to receive SMS and/or WhatsApp messages from LuxuStars regarding your account. Message frequency may vary.
                                                </label>
                                            </div>
                                        </div>
                                        <button class="notranslate btn button button1" id="stepper_send_otp_button">
                                            {{ translate('user_account_setting.send_verification_code') }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- KYC verification step -->
                            <div id="kyc-verification-part" class="content">

                                <div class="main_image_wrapper">
                                    <img class="img-fluid" src="{{ asset('website/images/kyc_identity_verification_main.png') }}" alt="">
                                </div>

                                <div class="text-center mb-4">
                                    <h5 class="notranslate">{{ translate('user_account_setting.verify_your_identity') }}
                                    </h5>
                                    <p class="notranslate">
                                        {{ translate('user_account_setting.please_click_below_to_proceed_verification') }}
                                    </p>
                                </div>
                                <a href="{{ route('kyc.start.kyc', ['locale' => app()->getLocale()]) }}"
                                    class="notranslate btn button button1">
                                    {{ translate('user_account_setting.upload_id_btn') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('website.layout.resetEmail')
    @include('website.layout.resetPhone')
    @include('website.layout.phone-verification-modal')
    @include('website.layout.add_card')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cleave.js/1.6.0/cleave.min.js"></script>
    <script>
        $('.toggle-password').on('click', function() {
            let input = $($(this).attr('toggle'));
            let icon = $(this);

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

        // function readURL(input) {
        //     if (input.files && input.files[0]) {
        //         var reader = new FileReader();
        //         reader.onload = function(e) {
        //             $('#blah')
        //                 .attr('src', e.target.result);
        //         };
        //         reader.readAsDataURL(input.files[0]);
        //     }
        // }
        var $telephone = $("#telephone_profile");
        $telephone.intlTelInput({
            separateDialCode: true,
        });
        $telephone.on("countrychange", function() {
            // var country_code = $(".iti__selected-dial-code").html();
            // $(".country_code_profile").val(country_code);
            mergeDialCode();
        });
        $telephone.on("keyup", function() {
            mergeDialCode();
        });
        $(document).ready(function() {
            mergeDialCode();

            // Initialize Select2 with flags in options and selected item
            $('#country-select').select2({
                placeholder: "{{ translate('user_account_setting.select_country') }}",
                // allowClear: true,
                width: '100%',
                templateResult: formatCountry,
                templateSelection: formatCountrySelection
            });

            // Function to update flag icon on left side of dropdown (outside select2)
            function updateCountryFlag() {
                var selectedOption = $('#country-select').find('option:selected');
                var flagCode = selectedOption.data('flag');
                var flagElement = $('#selected-flag');

                if (flagCode && selectedOption.val() !== '') {
                    flagElement.html('<span class="flag-icon flag-icon-' + flagCode + '"></span>');
                } else {
                    flagElement.html('');
                }
            }

            // Custom template for dropdown options
            function formatCountry(country) {
                if (!country.id) {
                    return country.text;
                }
                var flag = $(country.element).data('flag');
                if (flag) {
                    var $country = $(
                        '<span><span class="flag-icon flag-icon-' + flag + ' mr-2"></span>' + country.text +
                        '</span>'
                    );
                    return $country;
                }
                return country.text;
            }

            // Custom template for selected item display inside Select2 box
            function formatCountrySelection(country) {
                if (!country.id) {
                    return country.text;
                }
                var flag = $(country.element).data('flag');
                if (flag) {
                    return $('<span><span class="flag-icon flag-icon-' + flag + ' mr-2"></span>' + country.text +
                        '</span>');
                }
                return country.text;
            }

            // Initialize flag display on page load
            updateCountryFlag();

            // Handle flag badge outside dropdown on selection change
            $('#country-select').on('change', function() {
                updateCountryFlag();
            });
        });


        function mergeDialCode() {
            var dialCode = $(".iti__selected-dial-code").html();
            var number = $("#telephone_profile").val();
            var merged = dialCode + number;
            $(".country_code_merged").val(merged);
        }
        $('#profile_img').on('change', function() {
            let fileInput = this;
            if (fileInput.files && fileInput.files[0]) {
                let formData = new FormData();
                formData.append('profile_img', fileInput.files[0]);
                $.ajax({
                    url: '{{ route('userprofile.profile_img') }}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message
                        });
                        console.log(response.image_url);
                        if (response.image_url) {
                            $('#blah').attr('src', response.image_url);
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: xhr.responseJSON.message
                        });
                    }
                });
            }
        });
        // $(document).ready(function() {
        //     $('[name="card_holder_name"]').on('keydown keyup input', function(event) {
        //         var value = $(this).val();
        //         var filteredValue = value.replace(/[0-9]/g, '');
        //         if (value !== filteredValue) {
        //             $(this).val(filteredValue);
        //         }
        //     });
        // });
    </script>
    <script>
        $(document).ready(function() {
            $('.wh-box input, .wh-box select').prop('disabled', true).trigger('change.select2');

            $('.edit-sec').click(function() {
                var parent = $(this).closest('.wh-box');
                var fields = $(parent).find('input:not(.disabled_toggle), select:not(.disabled_toggle)');
                $(this).toggleClass('active');

                fields.each(function() {
                    var $field = $(this);
                    var isDisabled = $field.prop('disabled');
                    // Check if identity is verified and this is the country field
                    @if(auth()->user()->identity_verified == 'verified')
                        if ($field.attr('name') === 'country') {
                            $field.prop('disabled', true);
                            if ($field.is('select')) {
                                $field.trigger('change.select2');
                            }
                            return; // Skip enabling this field
                        }
                    @endif
                    $field.prop('disabled', !isDisabled);
                    if ($field.is('select')) {
                        $field.trigger('change.select2');
                    }
                });
            });
        });

        function moveToNext(current, nextFieldID) {
            if (current.value.length >= 2) {
                var nextField = document.getElementById(nextFieldID);
                if (nextField) {
                    nextField.focus();
                }
            }
        }
    </script>
    {{-- <script type="text/javascript" src="https://js.stripe.com/v2/"></script>
    <script type="text/javascript">
        $(function() {
            /*------------------------------------------
            --------------------------------------------
            Stripe Payment Code
            --------------------------------------------
            --------------------------------------------*/
            var $form = $(".require-validation");
            $('form.require-validation').bind('submit', function(e) {
                var $form = $(".require-validation"),
                    inputSelector = ['input[type=email]', 'input[type=password]',
                        'input[type=text]', 'input[type=file]',
                        'textarea'
                    ].join(', '),
                    $inputs = $form.find('.required').find(inputSelector),
                    $errorMessage = $form.find('div.error'),
                    valid = true;
                $errorMessage.addClass('hide');
                $('.has-error').removeClass('has-error');
                $inputs.each(function(i, el) {
                    var $input = $(el);
                    if ($input.val() === '') {
                        $input.parent().addClass('has-error');
                        $errorMessage.removeClass('hide');
                        e.preventDefault();
                    }
                });
                if (!$form.data('cc-on-file')) {
                    e.preventDefault();
                    Stripe.setPublishableKey($form.data('stripe-publishable-key'));
                    Stripe.createToken({
                        number: $('.card-number').val(),
                        cvc: $('.card-cvc').val(),
                        exp_month: $('.card-expiry-month').val(),
                        exp_year: $('.card-expiry-year').val()
                    }, stripeResponseHandler);
                }
            });
            /*------------------------------------------
            --------------------------------------------
            Stripe Response Handler
            --------------------------------------------
            --------------------------------------------*/
            function stripeResponseHandler(status, response) {
                if (response.error) {
                    let message = response.error.message;
                    if(response.error.message == "A network error has occurred, and you have not been charged. Please try again."){
                        message = "Please enter the card details and try again"
                    }
                    Swal.fire({
                        title: "Error",
                        text: message,
                        icon: "error"
                    });
                    $('.error')
                        .removeClass('hide')
                        .find('.alert')
                        .text(message);
                } else {
                    /* token contains id, last4, and card type */
                    var token = response['id'];
                    $form.find('input[type=text]').empty();
                    $form.append("<input type='hidden' name='stripeToken' value='" + token + "'/>");
                    $form.get(0).submit();
                }
            }
        });
    </script> --}}
    <script src="https://js.skyflow.com/v1/index.js"></script>
    <script>
        $(document).ready(function() {
            $(document).on("click", "#collectPCIData", function() {
                var $button = $(this);
                $button.prop("disabled", true);
                $('.spinner-border').removeClass('d-none'); // Show the spinner
                setTimeout(function() {
                    $button.prop("disabled", false);
                    $('.spinner-border').addClass('d-none');
                }, 3000);
            });
        });
        try {
            const skyflow = Skyflow.init({
                vaultID: "{{ skyflow_vault_id() }}",
                vaultURL: "{{ skyflow_vault_url() }}",
                getBearerToken: () => {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: "{{ route('getBearerToken') }}",
                            method: "GET",
                            data: {
                                type: "front_end"
                            },
                            success: function(response) {
                                resolve(response.accessToken);
                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                reject(errorThrown);
                            }
                        });
                    });
                },
                options: {
                    logLevel: Skyflow.LogLevel.ERROR,
                    // actual value of element can only be accessed inside the handler
                    // when the env is set to DEV.
                    // make sure the env is set to PROD when using skyflow-js in production
                    env: Skyflow.Env.DEV,
                }
            });
            // create collect Container
            const collectContainer = skyflow.container(Skyflow.ContainerType.COLLECT);
            //custom styles for collect elements
            const collectStylesOptions = {
                inputStyles: {
                    base: {
                        border: "1px solid #eae8ee",
                        padding: "10px 16px",
                        color: "#1d1d1d",
                        marginTop: "4px",
                        fontFamily: "Poppins, sans-serif",
                        paddingInline: "18px",
                        height: "50px",
                        borderRadius: "15px",
                        marginBottom: "5px",
                    },
                    complete: {
                        color: "#4caf50",
                    },
                    empty: {},
                    focus: {},
                    invalid: {
                        color: "#f44336",
                    },
                },
                labelStyles: {
                    base: {
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: "bold",
                    },
                },
                errorTextStyles: {
                    base: {
                        color: "#f44336",
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                    },
                },
            };
            // create collect elements
            const cardNumberElement = collectContainer.create({
                table: "credit_cards",
                column: "card_number",
                ...collectStylesOptions,
                placeholder: "Card Number",
                label: "Card Number",
                type: Skyflow.ElementType.CARD_NUMBER,
            }, {
                required: true
            });
            // const cvvElement = collectContainer.create({
            //     table: "credit_cards",
            //     column: "cvv",
            //     ...collectStylesOptions,
            //     label: "Cvv",
            //     placeholder: "cvv",
            //     type: Skyflow.ElementType.CVV,
            // });
            const expiryDateElement = collectContainer.create({
                table: "credit_cards",
                column: "expiry_date",
                ...collectStylesOptions,
                label: @json(translate('user_account_setting.expiry_date')),
                placeholder: @json(translate('user_account_setting.mm_yyyy')),
                type: Skyflow.ElementType.EXPIRATION_DATE,
            }, {
                required: true
            });
            const cardHolderNameElement = collectContainer.create({
                table: "credit_cards",
                column: "cardholder_name",
                ...collectStylesOptions,
                label: @json(translate('user_account_setting.card_holder_name')),
                placeholder: @json(translate('user_account_setting.cardholder_name')),
                type: Skyflow.ElementType.CARDHOLDER_NAME,
            }, {
                required: true
            });
            // mount the elements
            cardNumberElement.mount("#collectCardNumber");
            // cvvElement.mount("#collectCvv");
            expiryDateElement.mount("#collectExpiryDate");
            cardHolderNameElement.mount("#collectCardholderName");
            // add listeners to Collect Elements
            // add READY EVENT Listener
            cardNumberElement.on(Skyflow.EventName.READY, (readyState) => {
                console.log("Ready Event Triggered", readyState);
            });
            // add CHANGE EVENT Listener for card number to detect card brand
            cardNumberElement.on(Skyflow.EventName.CHANGE, (changeState) => {
                console.log("CHANGE Event Triggered for Card Number", changeState);
                // Log the entire changeState object to see its structure
                console.log("Full changeState object:", JSON.stringify(changeState));
                // In DEV environment, we can access the actual value and detect card brand
                if (changeState.value && changeState.value.length >= 4) {
                    // Get card brand from the state - check all possible properties
                    let cardBrand = null;
                    // Try different possible properties where card type might be stored
                    if (changeState.cardType) {
                        cardBrand = changeState.cardType;
                    } else if (changeState.card && changeState.card.cardType) {
                        cardBrand = changeState.card.cardType;
                    } else if (changeState.card && changeState.card.brand) {
                        cardBrand = changeState.card.brand;
                    } else if (changeState.brand) {
                        cardBrand = changeState.brand;
                    }
                    // If we found a card brand, display it
                    if (cardBrand) {
                        console.log("Card Brand Detected:", cardBrand);
                        // Map Skyflow card type to user-friendly name
                        let brandName = "";
                        // Convert to uppercase for case-insensitive comparison
                        const brandUpper = cardBrand.toUpperCase();
                        switch (brandUpper) {
                            case "VISA":
                                brandName = "Visa";
                                break;
                            case "MASTERCARD":
                                brandName = "Mastercard";
                                break;
                            case "AMEX":
                            case "AMERICAN EXPRESS":
                                brandName = "American Express";
                                break;
                            case "DINERS_CLUB":
                            case "DINERS CLUB":
                                brandName = "Diners Club";
                                break;
                            case "DISCOVER":
                                brandName = "Discover";
                                break;
                            case "JCB":
                                brandName = "JCB";
                                break;
                            case "MAESTRO":
                                brandName = "Maestro";
                                break;
                            case "UNIONPAY":
                            case "UNION PAY":
                                brandName = "UnionPay";
                                break;
                            case "HIPERCARD":
                                brandName = "Hipercard";
                                break;
                            case "CARTES_BANCAIRES":
                            case "CARTES BANCAIRES":
                                brandName = "Cartes Bancaires";
                                break;
                            default:
                                brandName = cardBrand; // Use the original value if not recognized
                        }
                        // Store the detected brand name in our variable
                        detectedCardBrand = brandName;
                        // Show card brand in alert
                        // alert("This is a " + brandName + " card");
                    } else {
                        // If we couldn't detect the card brand from the changeState object,
                        // try to determine it from the card number prefix
                        const cardNumber = changeState.value;
                        let detectedBrand = "Unknown";
                        // Simple prefix-based detection
                        if (/^4/.test(cardNumber)) {
                            detectedBrand = "Visa";
                        } else if (/^5[1-5]/.test(cardNumber)) {
                            detectedBrand = "Mastercard";
                        } else if (/^3[47]/.test(cardNumber)) {
                            detectedBrand = "American Express";
                        } else if (/^6(?:011|5)/.test(cardNumber)) {
                            detectedBrand = "Discover";
                        } else if (/^(?:2131|1800|35)/.test(cardNumber)) {
                            detectedBrand = "JCB";
                        } else if (/^3(?:0[0-5]|[68])/.test(cardNumber)) {
                            detectedBrand = "Diners Club";
                        } else if (/^62/.test(cardNumber)) {
                            detectedBrand = "UnionPay";
                        }
                        if (detectedBrand !== "Unknown") {
                            // Store the detected brand name in our variable
                            detectedCardBrand = detectedBrand;
                        }
                    }
                }
            });
            // add CHANGE EVENT Listener for card number to detect card brand end
            // add FOCUS EVENT Listener
            expiryDateElement.on(Skyflow.EventName.FOCUS, (focusState) => {
                console.log("FOCUS Event Triggered", focusState);
            });
            // add BLUR EVENT Listener
            cardHolderNameElement.on(Skyflow.EventName.BLUR, (blurState) => {
                console.log("BLUR Event Triggered", blurState);
            });
            // Variable to store the detected card brand
            let detectedCardBrand = "";
            // collect all elements data
            const collectButton = document.getElementById("collectPCIData");
            if (collectButton) {
                collectButton.addEventListener("click", () => {
                    // loader
                    $("#collectPCIData").html(`
                    <div class="spinner-border text-secondary" role="status">
                        <span class="visually-hidden">{{ translate('user_account_setting.loading') }}</span>
                    </div>`);
                    const collectResponse = collectContainer.collect({
                        additionalFields: {
                            records: [{
                                table: "credit_cards",
                                fields: {
                                    user_id: {{ auth()->id() }},
                                    card_type: detectedCardBrand // Store the detected card brand
                                }
                            }]
                        }
                    });
                    // axios start
                    collectResponse
                        .then((response) => {
                            Swal.fire({
                                title: @json(translate('user_account_setting.success')),
                                text: @json(translate('user_account_setting.card_added_successfully')),
                                icon: "success"
                            }).then(() => {
                                location.reload();
                            });
                        })
                        .catch((err) => {
                            console.log(err);
                        }).then(() => {
                            $("#collectPCIData").html(@json(translate('user_account_setting.add_card')));
                        });
                });
            }
        } catch (err) {
            console.log(err);
        }
        setTimeout(() => {
            $('#collectPCIData').prop("disabled", false);
        }, 5000);
        $(document).ready(function() {
            var emailAddress = $('.email_default').val();
            let phone = $('.country_code_merged').val();

            $('#profileForm').on('submit', function(e) {
                e.preventDefault();
                isValid = true; // ✅ Reset at start

                var updatedEmailAddress = $('.email_default').val();
                let updatedphone = $('.country_code_merged').val();
                let formData = new FormData(this);
                formData.delete('email');

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    success: function(response) {
                        if (emailAddress != updatedEmailAddress && phone != updatedphone) {
                            sessionStorage.setItem('pendingEmailChange', updatedEmailAddress);
                            $('#change_phone #changephone').val(updatedphone);
                            sessionStorage.setItem('updatedPhoneNumber', updatedphone);
                            $('#change_phone #change-phone-btn').click();
                        } else if (emailAddress != updatedEmailAddress) {
                            $('#change_email #changeEmail').val(updatedEmailAddress);
                            $('#change_email #change-email-btn').click();
                        } else if (phone != updatedphone) {
                            $('#change_phone #changephone').val(updatedphone);
                            sessionStorage.setItem('updatedPhoneNumber', updatedphone);
                            $('#change_phone #change-phone-btn').click();
                        }

                        // ❗️Add delay to wait for phone check (if async needed)
                        setTimeout(() => {
                            if (isValid) {
                                Swal.fire({
                                    icon: 'success',
                                    title: @json(translate('user_account_setting.success')),
                                    text: @json(translate('user_account_setting.profile_updated_successfully')),
                                    confirmButtonText: @json(translate('user_account_setting.ok'))
                                });
                            }
                        }, 500); // delay to wait for phone AJAX result
                    },

                    error: function(xhr) {
                        isValid = false;
                        let errors = xhr.responseJSON?.errors || {};
                        $('.text-danger').remove();
                        $.each(errors, function(key, messages) {
                            let inputField = $(`[name="${key}"]`);
                            messages.forEach(function(message) {
                                inputField.after(
                                    `<p class="text-danger mt-1">${message}</p>`
                                );
                            });
                            inputField.addClass('is-invalid');
                        });

                        if (!errors) {
                            Swal.fire({
                                icon: 'error',
                                title: @json(translate('user_account_setting.error')),
                                text: @json(translate('user_account_setting.an_error_occurred')),
                                confirmButtonText: @json(translate('user_account_setting.ok'))
                            });
                        }
                    }
                });
            });


        });
        // Session logout function
        function logoutSession(sessionId) {
            Swal.fire({
                title: @json(translate('user_account_setting.are_you_sure')),
                text: @json(translate('user_account_setting.logout_session_confirmation')),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: @json(translate('user_account_setting.yes_logout')),
                cancelButtonText: @json(translate('user_account_setting.cancel'))
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '{{ route('session.logout') }}',
                        type: 'POST',
                        data: {
                            session_id: sessionId,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            Swal.fire({
                                icon: 'success',
                                title: @json(translate('user_account_setting.success')),
                                text: @json(translate('user_account_setting.session_logged_out_successfully')),
                                confirmButtonText: @json(translate('user_account_setting.ok'))
                            }).then(() => {
                                location.reload();
                            });
                        },
                        error: function(xhr) {
                            Swal.fire({
                                icon: 'error',
                                title: @json(translate('user_account_setting.error')),
                                text: @json(translate('user_account_setting.failed_to_logout_session')),
                                confirmButtonText: @json(translate('user_account_setting.ok'))
                            });
                        }
                    });
                }
            });
        }
        $(document).ready(function() {
            $(document).on('click',
                '.setting_page .profile_setting .card_wrapper .card_parent .deafult_btn, .setting_page .profile_setting .card_wrapper .card_parent .remove_btn, #collectPCIData',
                function() {
                    localStorage.setItem("paymentMethodCollapse", "true");
                });
            var checkCollapse = localStorage.getItem("paymentMethodCollapse");
            if (checkCollapse == "true") {
                $('.accordion-button[data-bs-target="#collapseThree"]').click();
                localStorage.removeItem("paymentMethodCollapse");
            }
        });
    </script>
    <script>
        function sendPhoneVerificationOTP() {
            // Show loading state
            Swal.fire({
                title: 'Sending verification code',
                text: 'Please wait...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            // Send OTP to user's phone
            $.ajax({
                url: "{{ route('resend_otp_kyc') }}",
                type: "POST",
                data: {
                    type: "phone",
                    phone: "{{ auth()->user()->phone }}",
                    country_code: "{{ substr(auth()->user()->phone, 0, strpos(auth()->user()->phone, ' ')) }}"
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    Swal.close();
                    if (response.status) {
                        // Show the verification modal
                        $('#phone_verification_modal').modal('show');
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to send verification code'
                        });
                    }
                },
                error: function(xhr) {
                    Swal.close();
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON?.message || 'Failed to send verification code'
                    });
                }
            });
        }
        // Handle resend OTP button
        $(document).on('click', '#resend_otp_button', function() {
            sendPhoneVerificationOTP();
        });
        // Handle verify button click
        $(document).on('click', '#verify_phone_button', function() {
            const otp = $('#phone_verification_otp').val();
            const phone = $('#telephone_profile').val();


            if (!otp) {
                $('#phone_verification_error').text('Please enter the verification code');
                return;
            }
            // Disable button and show loading
            const $button = $(this);
            $button.prop('disabled', true).html(
                '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...'
            );
            // Verify OTP
            $.ajax({
                url: "{{ route('check_kyc_phone_otp') }}",
                type: "POST",
                data: {
                    phone: "{{ auth()->user()->phone }}",
                    code: otp
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        // Update user's phone_verified_at status
                        $.ajax({
                            url: "{{ route('update_phone_verified_status') }}",
                            type: "POST",
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: function() {
                                $('#phone_verification_modal').modal('hide');

                                Swal.fire({
                                    icon: 'success',
                                    title: 'Phone Number Verified',
                                    text: 'Your phone number has been verified. Please proceed to identity verification.',
                                    confirmButtonText: 'Proceed to Verification'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        window.location.href =
                                            "{{ route('kyc.start.kyc', ['locale' => app()->getLocale()]) }}";
                                    } else {
                                        location.reload();
                                    }
                                });
                            }
                        });
                        // $('#phone_verification_modal').modal('hide');

                        // Swal.fire({
                        //     icon: 'success',
                        //     title: 'Phone Number Verified',
                        //     text: 'Your phone number has been verified. Please proceed to identity verification.',
                        //     confirmButtonText: 'Proceed to Verification',
                        //     customClass: {
                        //         confirmButton: 'swal-confirm-phone-verified'
                        //     }
                        // }).then((result) => {
                        //     if (result.isConfirmed) {
                        //         window.location.href = "{{ route('stripe_custom_page') }}";
                        //     } else {
                        //         location.reload();
                        //     }
                        // });
                    } else {
                        $('#phone_verification_error').text(response.message ||
                            'Invalid verification code');
                        $button.prop('disabled', false).text('Verify');
                    }
                },
                error: function(xhr) {
                    $('#phone_verification_error').text(xhr.responseJSON?.message ||
                        'Failed to verify code');
                    $button.prop('disabled', false).text('Verify');
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {

            // Initialize stepper
            var stepper = null;

            // Initialize stepper when modal is shown
            $('#deleteAccountModal').on('shown.bs.modal', function() {
                if (!stepper) {
                    stepper = new Stepper(document.querySelector('#delete-account-stepper'), {
                        linear: true,
                        animation: true
                    });

                    // Show the first step content and hide others
                    $('#password-part').removeClass('d-none').addClass('active');
                    $('#otp-part, #confirm-part').removeClass('active').addClass('d-none');
                }
            });

            // Open delete account modal
            $(".delete_account_btn").on("click", function() {
                // First check if user has active bookings
                $.ajax({
                    url: "{{ route('check_active_bookings') }}",
                    type: "GET",
                    success: function(response) {
                        if (response.has_active_bookings) {
                            // Show warning if user has active bookings
                            Swal.fire({
                                title: "{{ translate('user_account_setting.cannot_delete_account') }}",
                                text: "{!! translate('user_account_setting.cannot_delete_account_msg') !!}",
                                icon: "warning",
                                confirmButtonText: "{{ translate('user_account_setting.ok') }}"
                            });
                        } else {
                            // Reset form fields
                            $("#current_password").val('').removeClass('is-invalid');
                            $("#otp_code").val('').removeClass('is-invalid');
                            $("#password-error, #otp-error").text('');
                            $("#otp_display_container").addClass('d-none');

                            // Show modal
                            $("#deleteAccountModal").modal("show");
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: "Error",
                            text: "Something went wrong. Please try again.",
                            icon: "error"
                        });
                    }
                });
            });

            // Password verification
            $("#verify-password-btn").on("click", function() {
                const password = $("#current_password").val();

                if (!password) {
                    $("#current_password").addClass("is-invalid");
                    $("#password-error").text("Please enter your password");
                    return;
                }

                // Show loading state
                const $button = $(this);
                $button.prop('disabled', true).html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...'
                );

                $.ajax({
                    url: "{{ route('verify_delete_password') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        password: password
                    },
                    success: function(response) {
                        if (response.success) {
                            // Password verified, move to next step
                            $('#password-part').removeClass('active').addClass('d-none');
                            $('#otp-part').removeClass('d-none').addClass('active');
                            $('#confirm-part').removeClass('active').addClass('d-none');

                            stepper.next();

                            // Display OTP (instead of sending email)
                            $("#otp_display").text(response.otp);
                            $("#otp_display_container").removeClass("d-none");
                        } else {
                            // Show error
                            $("#current_password").addClass("is-invalid");
                            $("#password-error").text(response.message);
                        }
                        $button.prop('disabled', false).text('Next');
                    },
                    error: function(xhr) {
                        $("#current_password").addClass("is-invalid");
                        $("#password-error").text(xhr.responseJSON?.message ||
                            "Something went wrong");
                        $button.prop('disabled', false).text('Next');
                    }
                });
            });

            // Verify OTP
            $("#verify-otp-btn").on("click", function() {
                const otp = $("#otp_code").val();

                if (!otp) {
                    $("#otp_code").addClass("is-invalid");
                    $("#otp-error").text("Please enter the OTP");
                    return;
                }

                // Show loading state
                const $button = $(this);
                $button.prop('disabled', true).html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...'
                );

                $.ajax({
                    url: "{{ route('verify_delete_otp') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        otp: otp
                    },
                    success: function(response) {
                        if (response.success) {
                            // OTP verified, move to next step
                            $('#password-part').removeClass('active').addClass('d-none');
                            $('#otp-part').removeClass('active').addClass('d-none');
                            $('#confirm-part').removeClass('d-none').addClass('active');

                            stepper.next();
                        } else {
                            // Show error
                            $("#otp_code").addClass("is-invalid");
                            $("#otp-error").text(response.message);
                        }
                        $button.prop('disabled', false).text('Next');
                    },
                    error: function(xhr) {
                        $("#otp_code").addClass("is-invalid");
                        $("#otp-error").text(xhr.responseJSON?.message ||
                            "Something went wrong");
                        $button.prop('disabled', false).text('Next');
                    }
                });
            });

            // Final confirmation
            $("#confirm-delete-btn").on("click", function() {
                // Show loading state
                const $button = $(this);
                $button.prop('disabled', true).html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting Account...'
                );

                $.ajax({
                    url: "{{ route('delete_account') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        if (response.success) {
                            $("#deleteAccountModal").modal("hide");

                            Swal.fire({
                                title: "Account Deleted",
                                text: response.message,
                                icon: "success",
                                confirmButtonText: "OK"
                            }).then(() => {
                                window.location.href = response.redirect ||
                                    "{{ route('login') }}";
                            });
                        } else {
                            Swal.fire({
                                title: "Error",
                                text: response.message,
                                icon: "error"
                            });
                            $button.prop('disabled', false).text('Delete My Account');
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: "Error",
                            text: "Something went wrong. Please try again.",
                            icon: "error"
                        });
                        $button.prop('disabled', false).text('Delete My Account');
                    }
                });
            });
        });
    </script>
    <script>
        // Function to open verification stepper modal
        function openVerificationStepper() {
            // Show modal
            $("#verificationStepperModal").modal("show");

            // Determine which step to show based on verification status
            if ("{{ auth()->user()->phone_verified_at }}" === "") {
                // Phone not verified, show step 1
                $("#verification-stepper .step").removeClass("active");
                $("#verification-stepper .step:first-child").addClass("active");
                $("#verification-stepper .content").removeClass("active");
                $("#phone-verification-part").addClass("active");

                // Show send OTP container
                $("#phone-verification-send-container").removeClass("d-none");
                $("#phone-verification-otp-container").addClass("d-none");
            } else {
                // Phone already verified, show step 2
                $("#verification-stepper .step").removeClass("active");
                $("#verification-stepper .step:last-child").addClass("active");
                $("#verification-stepper .content").removeClass("active");
                $("#kyc-verification-part").addClass("active");
            }
        }

        // Send OTP button in stepper
        $(document).on("click", "#stepper_send_otp_button", function() {
            const $button = $(this);
            const otpType = $('input[name="stepper_otp_type"]:checked').val() || 'sms';
            $('input[name="otp_type"][value="' + otpType + '"]').prop('checked', true);
            $button.prop("disabled", true).html(
                '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...'
            );
            // Send OTP with selected type
            $.ajax({
                url: "{{ route('resend_otp_kyc') }}",
                type: "POST",
                data: {
                    type: "phone",
                    phone: "{{ auth()->user()->phone }}",
                    country_code: "{{ substr(auth()->user()->phone, 0, strpos(auth()->user()->phone, ' ')) }}",
                    otp_type: otpType
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        // Show OTP input form
                        $("#phone-verification-send-container").addClass("d-none");
                        $("#phone-verification-otp-container").removeClass("d-none");

                        // Start OTP timer
                        startOtpTimer();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to send verification code'
                        });
                    }

                    // Enable button after delay
                    setTimeout(function() {
                        $button.prop("disabled", false).text("Send Verification Code");
                    }, 2000);
                },
                error: function(xhr) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON?.message || 'Failed to send verification code'
                    });

                    setTimeout(function() {
                        $button.prop("disabled", false).text("Send Verification Code");
                    }, 2000);
                }
            });
        });

        // Resend OTP button in stepper
        $(document).on("click", "#stepper_resend_otp_button", function() {
            if ($(this).prop("disabled")) return;

            const $button = $(this);
            $button.prop("disabled", true);

            // Use the existing sendPhoneVerificationOTP function
            sendPhoneVerificationOTP(true);

            // Start OTP timer
            startOtpTimer();
        });

        // Verify phone button in stepper
        $(document).on("click", "#stepper_verify_phone_button", function() {
            const otp = $("#stepper_phone_verification_otp").val();

            if (!otp) {
                $("#stepper_phone_verification_error").text("Please enter the verification code");
                return;
            }

            // Disable button and show loading
            const $button = $(this);
            $button.prop("disabled", true).html(
                '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...'
            );

            // Verify OTP
            $.ajax({
                url: "{{ route('check_kyc_phone_otp') }}",
                type: "POST",
                data: {
                    phone: "{{ auth()->user()->phone }}",
                    code: otp
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        // Update user's phone_verified_at status
                        $.ajax({
                            url: "{{ route('update_phone_verified_status') }}",
                            type: "POST",
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: function() {
                                // Show success message
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Phone Number Verified',
                                    text: 'Your phone number has been verified. Please proceed to identity verification.',
                                    showConfirmButton: false,
                                    timer: 2000
                                });

                                // Move to next step
                                setTimeout(function() {
                                    $("#verification-stepper .step").removeClass(
                                        "active");
                                    $("#verification-stepper .verification_step")
                                        .addClass(
                                            "verified");
                                    $("#verification-stepper .step:last-child")
                                        .addClass("active");
                                    $("#verification-stepper .content").removeClass(
                                        "active");
                                    $("#kyc-verification-part").addClass("active");
                                }, 2000);
                            }
                        });
                    } else {
                        $("#stepper_phone_verification_error").text(response.message ||
                            "Invalid verification code");
                        $button.prop("disabled", false).text("Verify");
                    }
                },
                error: function(xhr) {
                    $("#stepper_phone_verification_error").text(xhr.responseJSON?.message ||
                        "Failed to verify code");
                    $button.prop("disabled", false).text("Verify");
                }
            });
        });

        // OTP timer function
        function startOtpTimer() {
            let timeLeft = 60;
            const $timer = $("#stepper_otp_timer");
            const $resendBtn = $("#stepper_resend_otp_button");

            // Hide resend button and show timer
            $resendBtn.addClass("d-none");
            $resendBtn.prop("disabled", true);

            const timerId = setInterval(function() {
                if (timeLeft <= 0) {
                    clearInterval(timerId);
                    $timer.text("");
                    $resendBtn.removeClass("d-none"); // Show the button
                    $resendBtn.prop("disabled", false);
                } else {
                    $timer.text(`Resend in ${timeLeft}s`);
                    timeLeft--;
                }
            }, 1000);
        }

        // Modify the existing sendPhoneVerificationOTP function to work with the stepper
        function sendPhoneVerificationOTP(fromStepper = false) {
            const otpType = $('input[name="otp_type"]:checked').val() || 'sms';
            $.ajax({
                url: "{{ route('resend_otp_kyc') }}",
                type: "POST",
                data: {
                    type: "phone",
                    phone: "{{ auth()->user()->phone }}",
                    country_code: "{{ substr(auth()->user()->phone, 0, strpos(auth()->user()->phone, ' ')) }}"
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        if (!fromStepper) {
                            // Original behavior - show modal
                            $('#phone_verification_modal').modal('show');
                        }
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to send verification code'
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON?.message || 'Failed to send verification code'
                    });
                }
            });
        }
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>

    <script>
        const flagPath = "{{ asset('website') }}/images/flags/";

        function formatCountry(state) {
            if (!state.id) return state.text;

            const flag = $(state.element).data('flag');
            const countryName = state.text;

            return $(`
            <span>
                <img src="${flagPath}/${flag}.png" class="img-flag" style="width: 20px; height: 14px; margin-right: 8px;" />
                ${countryName}
            </span>
            `);
        }

        $('#country-select').select2({
            templateResult: formatCountry,
            templateSelection: formatCountry,
            minimumResultsForSearch: -1 // optional: disable search box
        });
    </script>

    <script>
        // Handle "Different phone number? Click here to change" link
        $('#change_phone_link').on('click', function() {
            // Close the verification modal
            $('#verificationStepperModal').modal('hide');

            // Wait for modal to close, then scroll to personal information section
            setTimeout(function() {
                // Open the personal information accordion if it's closed
                const personalInfoAccordion = $('.accordion-button[data-bs-target="#collapseOne"]');
                if (personalInfoAccordion.hasClass('collapsed')) {
                    personalInfoAccordion.click();
                }

                // Scroll to the personal information section
                $('html, body').animate({
                    scrollTop: $('#collapseOne').offset().top - 100
                }, 500);

                // Enable editing mode for phone field
                setTimeout(function() {
                    const phoneEditButton = $('#collapseOne').find('.edit-sec');
                    if (!phoneEditButton.hasClass('active')) {
                        phoneEditButton.click();
                    }

                    // Focus on the phone input field
                    $('#telephone_profile').focus();
                }, 600);

            }, 300);
        });
    </script>
@endpush
