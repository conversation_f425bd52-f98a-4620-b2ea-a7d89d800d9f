<tr class="@if ($list->status == 6) draft @endif"
    data-status="@if ($list->status == 0) @if (auth()->user()->hasRole('service')) in_review @else {{ $list->statusName->name }} @endif
@elseif ($list->status == 1)
@if ($list->pause == 0) accepted  @else paused @endif
@elseif ($list->status == 6)
draft
@else
{{ $list->statusName->name }}
    @endif">
    <td class="no_draft"><input type="checkbox" name="listing_ids[]" class="user-checkbox" value="{{ $list->ids }}" />
    </td>
    @if (!auth()->user()->hasRole('service'))
        <td>{{ ($listing->currentPage() - 1) * $listing->perPage() + $loop->iteration }}</td>
        <td>
            <img src="{{ asset('website') . '/' . $list->user->avatar }}" width="50" height="50" alt=""
                onerror="this.onerror=null;this.src=`{{ asset('website/images/favicon_white_bg.svg') }}`;">
        </td>
    @else
        <td>
            <img src="{{ asset('website') . '/' . ($list->thumbnail_image->url ?? '') }}" class="rounded" width="50"
                height="50"
                onerror="this.onerror=null;this.src=`{{ asset('website/images/favicon_white_bg.svg') }}`;">
        </td>
    @endif
    <td class="text-center">{{ $list->name }}</td>
    @if (auth()->user()->hasRole('service'))
        <td class="text-center"> {{ $list->internal_name ?? '-' }}</td>
    @endif
    <td>{{ $list->category->display_name ?? '-' }}</td>
    <td class="text-center no_draft">{{ count($list->active_bookings) }}</td>
    <td class="no_draft">COP
        {{ number_format((int) $list->bookings_sum_total_amount, 0) }}
    </td>
    <td>
        @if ($list->status == 0)
            <p class="text-primary">
                @if (auth()->user()->hasRole('service'))
                    {{ translate('dashboard_listing.in_review') }}
                @else
                    {{ $list->statusName->name }}
                @endif
            </p>
        @elseif ($list->status == 1)
            @if ($list->pause == 0)
                <p class="text-success">
                    @if (!auth()->user()->hasRole('service'))
                        {{ translate('dashboard_listing.published') }}
                    @else
                        {{ translate('dashboard_listing.accepted') }}
                    @endif
                </p>
            @else
                <p class="text-secondary">
                    {{ translate('dashboard_listing.paused') }}
                </p>
            @endif
        @elseif ($list->status == 6)
            <p class="text-danger">{{ translate('dashboard_listing.draft') }}</p>
        @else
            <p class="text-danger">{{ $list->statusName->name }}</p>
        @endif
    </td>
    <td class="text-center no_draft">
        {{ $list->rating == '' ? '0.0' : $list->rating }}
    </td>
    <td class="for_draft">
        @if ($list->status == '6')
            @if ($list->days_left < 5 && $list->days_left > 0)
                <span class="text-danger">{{ $list->days_left }} {{ translate('dashboard_listing.days_left') }}</span>
            @elseif ($list->days_left > 0)
                {{ $list->days_left }} {{ translate('dashboard_listing.days_left') }}
            @else
                <span class="text-danger">{{ translate('dashboard_listing.deleted') }}</span>
            @endif
        @else
            <p class="text-center">-</p>
        @endif
    </td>
    <td class="text-center no_draft">
        <a href="{{ url('calendar') . '/' . $list->ids }}">
            <i style="font-size: 20px" class="fa-solid fa-calendar-days dark-yellow"></i>
        </a>
    </td>
    <td class="form_btn ">
        <div class="dropdown">
            <button class=" dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false">
                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                @if ($list->status == 1)
                    <a href="{{ url('/detail' . '/' . $list->ids . '/' . $list->slug) }}" class="dropdown-item"
                        target="_blank"
                        title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                        <i class="fas fa-eye" style="color: #000;"></i>
                       {{ translate('dashboard_listing.view_webpage') }}
                    </a>
                @endif
                @if ($list->status == 0)
                    @if (auth()->user()->hasRole(['user', 'sub_admin']))
                        <a href="{{ route('approve_listing', $list->id) }}" class="dropdown-item " title="">
                            <i class="fas fa-check-circle" style="color: green;"></i>
                            {{ translate('dashboard_listing.approve_listing') }}
                        </a>
                        <a href="" class="dropdown-item reject-btn" data-listing-id="{{ $list->id }}"
                            data-toggle="modal" data-target="#reject" title="">
                            <i class="fas fa-times-circle" style="color: red;"></i>
                            {{ translate('dashboard_listing.reject_listing') }}
                        </a>
                    @endif
                @endif
                {{-- edit --}}
                @can('edit-' . str_slug('Listing'))
                    <a href="{{ url('listings') . "/{$list->category->slug}/$list->ids" }}" class="dropdown-item"
                        title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                        <i class="fas fa-pencil-alt" style="color: #000;"></i>
                        {{ translate('dashboard_listing.edit_listing') }}
                    </a>
                @endcan
                {{-- duplicate --}}
                @can('add-' . str_slug('Listing'))
                    @if ($list->status != 6)
                    <a href="{{ route('duplicate_listing', ['listing_ids' => $list->ids, 'slug' => $list->category->slug]) }}"
                        class="dropdown-item"
                        title="Duplicate {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                        <i class="fas fa-copy" style="color: #000;"></i>
                        {{ translate('dashboard_listing.duplicate_listing') }}
                    </a>
                    @endif
                @endcan
                @if (count($list->files) > 0)
                    <a href="" class="dropdown-item get-listing-document" data-toggle="modal"
                        data-target="#uploaded_doc" data-listing-id="{{ $list->ids }}"
                        title="View Document {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                        <i class="fas fa-file" style="color: #000;"></i>
                        {{ translate('dashboard_listing.view_documents') }}
                    </a>
                @endif
                @if ($list->status == 1)
                    <button class="dropdown-item copy-link" data-listing-id="{{ $list->ids }}" title="Copy Url">
                        <i class="fas fa-copy" style="color: #000;"></i>
                        {{ translate('dashboard_listing.copy_listing_url') }}
                    </button>
                @endif

                {{-- @can('view-' . str_slug('Listing'))
                        <a class="dropdown-item"
                            href="{{ route('listing.show', $list->ids) }}">{{ trans('Show') }}</a>
                    @endcan --}}

                @if (auth()->user()->hasRole('service') && $list->status != 6)
                    @php
                        $internalName = $list->internal_name;
                    @endphp
                    <a href="" class="dropdown-item internal-name-model-btn" data-toggle="modal"
                        data-target="#internal_name" data-listing-id="{{ $list->ids }}"
                        data-internal-name="{{ $list->internal_name }}" title="Edit Internal Name">
                        <i class="fas fa-pencil-alt" style="color: #000;"></i>
                        @if ($internalName == '')
                            {{ translate('dashboard_listing.add') }}
                        @else
                            {{ translate('dashboard_listing.edit') }}
                        @endif
                        {{ translate('dashboard_listing.internal_name') }}
                    </a>
                @endif
                {{-- Pause/UnPause  --}}
                @if ($list->statusName->name != 'Pending')
                    @if ($list->pause == 1)
                        <button class="dropdown-item btn pause_btn" data-listing-id="{{ $list->ids }}"
                            data-pause-type="unpause">
                            <i class="fas fa-pause" style="color: #000;"></i>
                            {{ translate('dashboard_listing.unpause_listing') }}
                        </button>
                    @else
                        <button class="dropdown-item btn pause_btn" data-listing-id="{{ $list->ids }}"
                            data-pause-type="pause">
                            <i class="fas fa-pause" style="color: #000;"></i>
                            {{ translate('dashboard_listing.pause_listing') }}
                        </button>
                    @endif
                @endif
                {{-- Pause/UnPause end --}}
                @if (auth()->user()->hasRole('user'))

                    @if ($list->status == 5)
                        <button class="dropdown-item unsuspend_btn" data-listing-id="{{ $list->ids }}">
                            <i class="fas fa-pause" style="color: #000;"></i>
                            {{ translate('dashboard_listing.unsuspend_listing') }}
                        </button>
                        {{-- <a class="dropdown-item  suspend_btn"
                                data-type="unsuspend"
                                data-listing-id="{{ $list->id }}">{{ trans('unsuspend') }}</a> --}}
                    @elseif ($list->status == 1)
                        <button class="dropdown-item suspend_btn text-danger" data-toggle="modal"
                            data-target="#suspend" data-listing-id="{{ $list->ids }}">
                            <i class="fas fa-pause" style="color: red;"></i>
                            {{ translate('dashboard_listing.suspend_listing') }}
                        </button>
                        {{-- <a class="dropdown-item text-danger suspend_btn"
                                data-type="suspend"
                                data-listing-id="{{ $list->id }}">{{ trans('suspend') }}</a> --}}
                    @endif
                @endif
                {{-- delete --}}
                @can('delete-' . str_slug('Listing'))
                    <button type="submit" class="dropdown-item text-danger del-active-listing"
                        data-listing-id="{{ $list->ids }}" data-toggle="modal" data-target="#delete_listing"
                        title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                        <i class="fas fa-trash" style="color: red;"></i>
                       {{ translate('dashboard_listing.delete_listing') }}
                    </button>
                @endcan
            </div>
        </div>
    </td>
</tr>
