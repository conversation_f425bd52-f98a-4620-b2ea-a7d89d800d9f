<?php

namespace App;

use App\Models\BookingHourSlot;
use App\Models\ListingActivity;
use App\Models\ListingAddress;
use App\Models\ListingAttribute;
use App\Models\ListingDiscount;
use App\Models\ListingFeatureItinerary;
use App\Models\ListingHourlyAvailability;
use App\Models\ListingRestrictedDay;
use App\Models\ListingRule;
use App\Models\ListingSeasonPrice;
use App\Models\ListingTourDuration;
use App\Models\ListingTourLanguage;
use App\Models\ListingType;
use App\Models\ReservationDate;
use App\Models\TourLanguage;
use App\Models\Wishlist;
use App\Models\ReviewReport;
use App\Traits\HasUuid;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;

class Listing extends Model
{
    use SoftDeletes, LogsActivity, HasUuid;

    // Optional: customize the logged attributes
    protected static $logAttributes = ['*'];
    protected static $logName = 'listing';
    protected static $logOnlyDirty = true;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'listings';
    protected $appends = ["rating", "price", 'base_price', "thumbnail_image", "days_left"];

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['user_id', "ids", "type_id", 'name', "other_type", "slug", 'lat', 'lng', 'description', 'category_id', "url", "status", "step_no", "is_duplication","locale"];

    /**
     * Boot method to handle cache clearing when listing is updated
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when listing is updated
        static::updated(function ($listing) {
            self::clearListingCache($listing);
        });

        // Clear cache when listing is deleted
        static::deleted(function ($listing) {
            self::clearListingCache($listing);
        });
    }
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = ucwords($value);
    }

    /**
     * Clear all caches related to this listing
     */
    protected static function clearListingCache($listing)
    {
        // Clear listing detail cache
        Cache::forget("listing_detail_{$listing->id}_{$listing->slug}");

        // Clear booking dates cache
        Cache::forget("listing_booking_dates_{$listing->id}");

        // Clear listing service cache (from ListingService)
        Cache::forget("listings_category_{$listing->category_id}");
        Cache::forget("listings_category_");
    }

    function user()
    {
        return $this->belongsTo(User::class, "user_id");
    }
    function detail()
    {
        return $this->hasOne(ListingDetail::class, "listing_id");
    }
    function address()
    {
        return $this->hasOne(ListingAddress::class, "listing_id");
    }
    function type()
    {
        return $this->belongsTo(ListingType::class);
    }
    function category()
    {
        return $this->belongsTo(Category::class, "category_id");
    }
    function discount()
    {
        return $this->hasOne(ListingDiscount::class);
    }
    function experiences()
    {
        return $this->hasMany(ListingExperience::class, "listing_id");
    }
    function accessibilities()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "accessibility");
    }
    function key_features()
    {
        return $this->hasMany(ListingFeatureItinerary::class)->where("type", "key-feature");
    }
    function includes()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "include");
    }
    function not_includes()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "not-include");
    }
    function tour_languages_pivot(){
        return $this->hasMany(ListingTourLanguage::class);
    }
    public function tour_languages()
    {
        return $this->belongsToMany(TourLanguage::class, 'listing_tour_languages', 'listing_id', 'tour_language_id')->withTimestamps();
    }
    
    function itineraries()
    {
        return $this->hasMany(ListingFeatureItinerary::class)->where("type", "itinerary")->orderBy("day", "ASC")->with("tour_durations");
    }
    function hourly_availabilities()
    {
        return $this->hasMany(ListingHourlyAvailability::class);
    }
    function restricted_days()
    {
        return $this->hasMany(ListingRestrictedDay::class);
    }
    function extras()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "extra");
    }
    function spec_features()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "spec_feature");
    }
    function equipments()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "equipment");
    }
    function services()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "service");
    }
    function activity()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "activity");
    }
    function tour_durations()
    {
        return $this->hasMany(ListingTourDuration::class);
    }
    function amenities()
    {
        return $this->hasMany(ListingAmenity::class);
    }
    function custom_amenities()
    {
        return $this->hasMany(ListingAmenity::class)->where("amenity_id", null)->orWhere("amenity_id", 0);
    }
    function amenity_options()
    {
        return $this->hasMany(AmenityOption::class);
    }
    function amenity_detail()
    {
        // return $this->belongsToMany("App\AmenityOption", "App\ListingAmenity", 'listing_id', 'amenity_id');
        return $this->belongsToMany(
            AmenityOption::class, // Related model
            'listing_amenities', // Pivot table
            'listing_id', // Foreign key on pivot table
            'amenity_option_id' // Foreign key on related table
        ); // Include fields from pivot table
    }
    // function star_amenity_detail()
    // {
    //     return $this->belongsToMany("App\AmenityOption", "App\ListingAmenity", 'listing_id', 'amenity_id')->where("star", 1);
    // }
    function gallery_images()
    {
        return $this->hasMany(listingGallery::class)->where("type", "image")->orderBy("sort_order");
    }
    function getThumbnailImageAttribute()
    {
        return listingGallery::where("listing_id", $this->id)->where("type", "image")->where("is_cover", 1)->first() ?? null;
    }
    function getPriceAttribute()
    {
        $listingPrice = base_price($this->detail);
        $seasons = $this->seasons;
        $today = now()->format('m/d/Y');

        foreach ($seasons as $season) {
            if ($today >= $season['start_date'] && $today <= $season['end_date']) {
                if ($season['type'] === 'Increase') {
                    $listingPrice += ($listingPrice * $season['percentage'] / 100);
                } elseif ($season['type'] === 'Decrease') {
                    $listingPrice -= ($listingPrice * $season['percentage'] / 100);
                }
            }
        }
        return $listingPrice;
    }
    function getBasePriceAttribute()
    {
        return base_price($this->detail);
    }

    function getDaysLeftAttribute()
    {
        $created_at = \Carbon\Carbon::parse($this->created_at);
        $now = \Carbon\Carbon::now();
        if($this->status == 6){
            return $created_at->diffInDays($now) < 30 ? 30 - $created_at->diffInDays($now) : 0;
        }
        return null;
    }
    function files()
    {
        return $this->hasMany(listingGallery::class)->where("type", "file");
    }
    function reservation_dates()
    {
        return $this->hasMany(ReservationDate::class, 'listing_id');
    }
    function wishlist()
    {
        return $this->hasOne(Wishlist::class, "listing_id")->where("user_id", auth()->id() ?? auth()->guard('sanctum')->id());
    }
    function wishlists()
    {
        return $this->hasMany(Wishlist::class, 'listing_id');
    }
    public function favorites()
    {
        return $this->hasMany(Wishlist::class, 'listing_id');
    }
    function bookings()
    {
        return $this->hasMany(Booking::class, 'listing_id')->whereIn("status", [0, 1, 3]);
    }
    public function searchbookings()
    {
        return $this->hasMany(Booking::class); // Make sure to adjust the related model name
    }
    function active_bookings()
    {
        return $this->hasMany(Booking::class)->where("status", 0);
    }
    function reviews()
    {
        return $this->hasMany(Review::class)->orderBy("id", "DESC");
    }

    public function reviewReports()
    {
        return $this->hasMany(ReviewReport::class);
    }

    function seasons()
    {
        return $this->hasMany(ListingSeasonPrice::class);
    }
    public function booked_slots($date = null)
    {
        $query = $this->hasMany(BookingHourSlot::class)
            ->where("status", 0);
        // if ($date) {
        //     $query->where("date", $date);
        // }
        // return $query->pluck("slot")->toArray();
        return $query;
    }
    
    
    function notes()
    {
        return $this->hasMany(ListingAttribute::class)->where("type", "note");
    }
    function latest_review()
    {
        return $this->hasOne(Review::class)->orderBy("id", "DESC");
    }
    function getratingAttribute($value = null)
    { 
        $reviews = Review::where("listing_id", $this->id)->get();
        $rating = 0;
        if (count($reviews) > 5) {
            $rating = $reviews->avg("rating");
            $rating = round($rating);
            $rating = max(1, min(5, $rating));
            $rating = number_format($rating, 1);
        }
        return $rating;
    }
    
    function rules()
    {
        return $this->hasMany(ListingRule::class);
    }
    function allow_rules()
    {
        return $this->hasMany(ListingRule::class)->where("allow", "yes");
    }
    function not_allow_rules()
    {
        return $this->hasMany(ListingRule::class)->where("allow", "no");
    }
    function statusName()
    {
        return $this->belongsTo(Status::class, "status");
    }

    function scopeActive($query)
    {
        return $query->where("status", 1)->where("pause", 0);
    }
    function scopeAllDetail($query)
    {
        return $query->with("detail", "gallery_images", "amenity_detail", "rules", "activity", "amenities", "services", "equipments", "itineraries", "spec_features", "extras", "not_includes", "includes", "key_features", "accessibilities");
    }
}
