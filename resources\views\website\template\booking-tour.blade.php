<div class="row py-3 g-0 book ps-0">
    <h5 class="pb-sm-4 pb-1">{{ translate('listing_details.book_your') }}
        {{ \Illuminate\Support\Str::singular($category->display_name ?? '-') }} {{ translate('listing_details.now') }}
    </h5>
    <form id="booking-form" class="d-flex gap-10 ps-0">
        <div class="col-xl-5 col-lg-6 col-md-12" data-aos="fade-right" id="calculation_detail">
            {{-- calculation goes here --}}
        </div>
        <div class="col-lg-5 col-md-12" data-aos="fade-left">
            <div class="head ps-xl-4 ps-lg-4 ps-md-0">
                <h5 class="pb-3">
                    <span class="total_days">1</span>
                    {{ translate('listing_details.day_at') }} {{ translateDynamic($listing->name,app()->getLocale(),$listing->locale) ?? '-' }}
                </h5>
                <p class="fs-14">
                    <span class="check_in">{{ translate('listing_details.select_a_date') }}</span>
                </p>
            </div>
            <div class="cal">
                <div class="calendar"></div>
            </div>
        </div>
    </form>
</div>


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pg-calendar@1.4.31/dist/js/pignose.calendar.min.js"></script>
    <script>
        $(document).ready(function() {
            const currency = "{{ session('currency', 'COP') }}";
            let loader = `<div id="listing-loading"><div class="loader"></div></div>`;
            let listing_price, listing_actual_price, adult_price, child_price;
            const policyType = @js($listing->detail->cancellation_policy);
            const date_format = "Do MMM YYYY";
            let number_adult = 1;
            let number_child = 0;
            let new_listing_discount;
            // ====================== Total Amount Calculation ====================== //
            function total_amount() {
                let totalAdultAmount = number_adult * adult_price;
                let totalChildAmount = number_child * child_price;
                let totalAmount = totalAdultAmount + totalChildAmount;
                if (new_listing_discount?.discount_percentage) {
                    discountAmount = Math.round((totalAmount * new_listing_discount.discount_percentage) / 100);
                    $(".nl-discount-amount").html(`- ${discountAmount}`);
                    totalAmount -= discountAmount;
                    totalAmount = Math.round(totalAmount);
                }
                $("#subtotal_amount").html(`${currency} ${totalAmount.toLocaleString('en')}`);
            }
            // ====================== Total Amount Calculation End ====================== //

            // ====================== Adult Calculation ====================== //
            const total_adult_amount = () => {
                adult_total_amount = number_adult * adult_price;
                $(".adult-qty").html(number_adult);
                $(".adult_input").val(number_adult);
                $(".adult_total_price").html(`${currency} ${adult_total_amount.toLocaleString('en')}`);
                total_amount(adult_total_amount);
            }
            $(document).on("click", "#adult_remove", function() {
                number_adult = parseInt($("#adult_number").val()) || 0;
                if (number_adult > 1) {
                    number_adult--;
                    total_adult_amount();
                }
            })
            $(document).on("click", "#adult_add", function() {
                number_adult = parseInt($("#adult_number").val()) || 0;
                number_adult++;
                total_adult_amount();
            })
            // ====================== Adult Calculation End ====================== //

            // ====================== Child Calculation ====================== //
            const total_child_amount = () => {
                child_total_amount = number_child * child_price;
                $(".child-qty").html(number_child);
                $(".child_input").val(number_child);
                $(".child_total_price").html(`${currency} ${child_total_amount.toLocaleString('en')}`);
                total_amount(child_total_amount);
            }
            $(document).on("click", "#child_remove", function() {
                number_child = parseInt($("#child_number").val()) || 0;
                if (number_child > 0) {
                    number_child--;
                    total_child_amount();
                }
            })
            $(document).on("click", "#child_add", function() {
                number_child = parseInt($("#child_number").val()) || 0;
                number_child++;
                total_child_amount();
            })
            // ====================== Child Calculation End ====================== //




            // add and remove child
            // $(document).on("click", "#child_remove", function() {
            //     if (total_child !== 0) {
            //         total_child--;
            //         $(".child_input").val(total_child);
            //         $(".child-qty").html(total_child);
            //         child_total_amount = total_child * child_price;
            //         child_actual_total_amount = total_child * child_actual_price;
            //         $(".child_total_price").html(`${current_currency} ${child_total_amount.toFixed(0)}`);
            //         total_amount()
            //     }
            // })
            // $(document).on("click", "#child_add", function() {
            //     total_child++;
            //     $(".child_input").val(total_child);
            //     $(".child-qty").html(total_child);
            //     child_total_amount = total_child * child_price;
            //     child_actual_total_amount = total_child * child_actual_price;
            //     $(".child_total_price").html(`${current_currency} ${child_total_amount.toFixed(0)}`);
            //     total_amount()
            // })

            function booking_submit(url, form_data) {
                $.ajax({
                    url: `${url}`,
                    data: form_data,
                    type: 'POST',
                    success: function(response) {
                        console.log(response);
                        if (response.status === true) {
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     position: 'top-right',
                            //     showHideTransition: 'slide',
                            //     icon: 'success'
                            // })
                            if (response.url) {
                                window.location.href = response.url
                            } else {
                                $("#cart_count").html(response.data)
                            }
                        } else {
                            Swal.fire(
                                `Oops!`,
                                `${response.message}`,
                                'error'
                            )
                            if (response.url) {
                                Swal.fire(
                                    `Verify identity`,
                                    `${response.message}`,
                                    'error'
                                ).then(function() {
                                    window.location.href = response.url;
                                })
                            }
                        }
                    }
                });
            }

            // reserve btn
            $(document).on("click", "#reserve_btn", function() {
                let reserve_data = $("#booking-form").serialize();
                booking_submit('{{ route('reserve_data') }}', reserve_data);
            })

            // cart btn
            // $("#cart_btn").on("click", function() {
            //     let cart_data = $("#booking-form").serialize();
            //     booking_submit('{{ route('add_cart') }}', cart_data);
            // })
            let tour_type = "guests"
            let start_date;
            $(document).on("click", ".tour_duration_type", function() {
                // let start_date = $(".check_in").val();
                tour_type = $(this).val();
                calculcation_detail();
            })
            const calculcation_detail = () => {
                $("#calculation_detail").html(loader);
                $.ajax({
                    type: 'GET',
                    url: `{{ route('calculate_detail_tour') }}`,
                    data: {
                        number_adult: number_adult,
                        number_child: number_child,
                        start_date: start_date,
                        tour_type: tour_type,
                        listing_id: '{{ $listing->ids }}',
                    },
                    success: function(response) {
                        if (response.status) {
                            adult_price = response.data.adult_price;
                            child_price = response.data.child_price;
                            new_listing_discount = response.data.new_listing_discount;
                            $("#calculation_detail").html(response.data.page).removeClass(
                                'select_date');
                        } else {
                            console.log('Error: No view received.');
                            $("#calculation_detail").html(@json(translate('listing_details.select_a_valid_date'))).addClass(
                                'select_date');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        Swal.fire(
                            `Error`,
                            @json(translate('listing_details.please_select_a_valid_view')),
                            'error'
                        )
                    },
                    complete: function() {
                        $('#check_in_time, #check_out_time').flatpickr({
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "h:i K",
                            time_24hr: false,
                            allowInput: true,
                        });
                    }
                });
            }


            function cancellation_policy(startDate) {
                $.ajax({
                    type: 'GET',
                    // url: `{{ url('cancellation-policy-timeline') }}/` + id + `/` + category,
                    url: `{{ url('cancellation-policy-timeline') }}/` + policyType +
                        `/` +
                        startDate,
                    data: {
                        policyType: policyType,
                        startDate: startDate
                    },
                    success: function(data) {
                        if (data) {
                            $('.main_col_listing_cancellation').show();
                            $("#cancellation_data").html(data);
                            console.log(policyType);
                        } else {
                            console.log('Error: No view received.');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        Swal.fire(
                            `Error`,
                            @json(translate('listing_details.please_select_a_valid_view')),
                            'error'
                        )
                    }
                });
            }


            var currentDate = new Date();
            // calendar 
            $('.calendar').pignoseCalendar({
                lang: "{{ app()->getLocale() }}",
                pickWeeks: false,
                minDate: currentDate,
                disabledDates: @json($reserve_dates_array),
                enabledDates: @json($enable_dates),
                select: function(dates, context) {
                    var start = dates[0];
                    var end = dates[1];
                    var startDate = start ? moment(start).format('YYYY-MM-DD') : '';
                    var endDate = end ? moment(end).format('YYYY-MM-DD') : '';
                    if (startDate) {
                        cancellation_policy(startDate);
                        start_date = startDate;
                        totalDays = 1;
                        totalAmount = listing_price * totalDays;
                        totalActualAmount = listing_actual_price * totalDays;
                        $(".check_in").html(moment(startDate).format(date_format));
                        $(".check_in").val(moment(startDate).format(date_format));
                        $("#reserve_btn").prop('disabled', false);
                        calculcation_detail(startDate);
                    } else {
                        $(".check_in").html(@json(translate('listing_details.select_a_date')));
                        $(".check_in").val('');
                        $("#reserve_btn").prop('disabled', true);
                    }
                }
            });

            // $('body').delegate('.child_wrapper button', 'click', function() {
            $(document).on('click', '.child_wrapper button', function() {
                var no_of_child = parseInt($('.child_input').val()) || 0;

                if ($(this).hasClass('plus_btn')) {
                    no_of_child += 1;
                } else {
                    no_of_child = Math.max(0, no_of_child - 1);
                }
                if (no_of_child != '0') {
                    $('.child_price_parent').removeClass('d-none');
                } else {
                    $('.child_price_parent').addClass('d-none');
                }
            });
            // $('body').delegate('.guest_wrapper button', 'click', function() {
            $(document).on('click', '.guest_wrapper button', function() {
                let input = $(this).closest('div').find('input');
                let person_limit = $(this).closest('.guests').attr('data-capacity');

                let adult = parseInt($('.adult_input').val()) || 0;
                let child = parseInt($('.child_input').val()) || 0;
                let total = adult + child;

                if (total >= person_limit) {
                    $('.guest_wrapper').find('.plus_btn').attr('disabled', true);
                } else {
                    $('.guest_wrapper').find('.plus_btn').attr('disabled', false);
                }
            });
        });
    </script>
@endpush
