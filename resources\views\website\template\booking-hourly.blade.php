@push('css')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
@endpush
@php
    $currency = session('currency', 'COP');
    $conversion_rate = session('conversion_rate', 1);
    $listing_price = $listing->price * $conversion_rate;
@endphp
<div class="row py-3 g-0 book ps-0">
    <h5 class="pb-sm-4 pb-1">{{ translate('listing_details.book_your') }} {{ \Illuminate\Support\Str::singular($category->display_name ?? '-') }}  {{ translate('listing_details.now') }}</h5>
    <form id="booking-form" class="d-flex gap-10 ps-0">
        <input type="hidden" name="listing_id" value="{{ $listing->ids }}">
        <div class="col-xl-5 col-lg-6 col-md-12">
            <div class="info px-4 pt-sm-4 pb-sm-5 py-3 px-4 booking_info select_date hourly_booking"
                id="calculation_detail" data-aos="fade-right">
                {{ translate('listing_details.please_select_the_date') }}
            </div>
        </div>
        <div class="col-lg-5 col-md-12" data-aos="fade-left">
            @if ($listing->category_id == 4)
                <div class="head ps-lg-4 ps-md-0">
                    <h5 class="pb-3">
                        {{-- car --}}
                        {{-- Rent <span class="total_days">0</span> day's --}}
                        {{-- @else --}}
                        <span class="total_days">0</span> {{ translate('listing_details.days_in') }} {{ translateDynamic($listing->name,app()->getLocale(),$listing->locale) ?? '-' }}
                    </h5>

                    <p class="fs-14">
                        <span class="check_in">- -</span>
                        <span class="check-cont">
                            to <span class="check_out">- -</span>
                        </span>
                    </p>
                </div>
            @endif
            <div class="cal">
                <div class="calendar"></div>
            </div>
        </div>
    </form>
</div>


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pg-calendar@1.4.31/dist/js/pignose.calendar.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            const policyType = @js($listing->detail->cancellation_policy);
            const currency_code = @js($currency);
            let loader = `<div id="listing-loading"><div class="loader"></div></div>`;
            var currentDate = @js($enable_dates[0] ?? now()->toDateString());
            let listing_price;
            let new_listing_discount;
            
            const calculate_detail_hourly = (startDate) => {
                let time_slots = $("#time_slots").val();
                $("#calculation_detail").html(loader);
                $.ajax({
                    type: 'GET',
                    url: `{{ route('calculate_detail_hourly') }}`,
                    data: {
                        listing_id: @js($listing->ids),
                        start_date: startDate,
                        time_slots: time_slots
                    },
                    success: function(response) {
                        console.log(response);
                        if (response.status === true) {
                            $("#calculation_detail").html(response.data.page).removeClass('select_date');
                            listing_price = response.data.listing_price;
                            new_listing_discount = response.data.new_listing_discount;
                        } else {
                            Swal.fire(
                                `Error`,
                                `${response.message}`,
                                'error'
                            )
                        }
                    },
                    complete: function() {
                        $('.multi_select').select2({
                            closeOnSelect: false,
                            placeholder: `{{ translate('listing_details.select_slots') }}`,
                            allowClear: false,
                            tags: false,
                            multiple: true,
                        });
                        $('.hourly_slots .select2-selection__rendered').append(
                            `<li class="slots_item"><span class="slot">{{ translate('listing_details.select') }}</span> {{ translate('listing_details.slots') }}</li>`);
                    }
                });
            }
            calculate_detail_hourly(currentDate)

            function cancellation_policy(startDate) {
                $.ajax({
                    type: 'GET',
                    // url: `{{ url('cancellation-policy-timeline') }}/` + id + `/` + category,
                    url: `{{ url('cancellation-policy-timeline') }}/` + policyType +
                        `/` +
                        startDate,
                    data: {
                        policyType: policyType,
                        startDate: startDate
                    },
                    success: function(data) {
                        if (data) {
                            $('.main_col_listing_cancellation').show();
                            $("#cancellation_data").html(data);
                            console.log(policyType);
                        } else {
                            console.log('Error: No view received.');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        Swal.fire(
                            `{{ translate('listing_details.error') }}`,
                            `{{ translate('listing_details.please_select_a_valid_view') }}`,
                            'error'
                        )
                    }
                });
            }

            if (currentDate) {
                cancellation_policy(currentDate);
            }

            function booking_submit(url, form_data) {
                $.ajax({
                    url: `${url}`,
                    data: form_data,
                    type: 'POST',
                    success: function(response) {
                        if (response.status === true) {
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     position: 'top-right',
                            //     showHideTransition: 'slide',
                            //     icon: 'success'
                            // })
                            if (response.url) {
                                window.location.href = response.url
                            }
                        } else {
                            Swal.fire(
                                `{{ translate('listing_details.error') }}`,
                                `${response.message}`,
                                'error'
                            )
                            if (response.url) {
                                Swal.fire(
                                    `{{ translate('listing_details.verify_identity') }}`,
                                    `${response.message}`,
                                    'error'
                                ).then(function() {
                                    window.location.href = response.url;
                                })
                            }
                        }
                    }
                });
            }
            // reserve btn
            $(document).on("click","#reserve_btn" ,function() {
                let reserve_data = $("#booking-form").serialize();
                booking_submit('{{ route('reserve_data') }}', reserve_data);
            });

            var reserved = @json($reserve_dates_array) || [];
            var enabled = @json($enable_dates) || [];

            // calendar
            $('.calendar').pignoseCalendar({
                lang: "{{ app()->getLocale() }}",
                pickWeeks: false,
                multiple: false,
                minDate: currentDate,
                disabledDates: reserved,
                enabledDates: enabled,
                select: function(dates, context) {
                    var start = dates[0];
                    var end = dates[1];
                    var startDate = `${start}`;
                    if (start) {
                        cancellation_policy(startDate);
                    }
                    if (start == null && end == null) {
                        $("#reserve_btn").prop('disabled', true);
                        return false;
                    }
                    checkInDate = start ? moment(start).format('YYYY-MM-DD') : '';
                    calculate_detail_hourly(checkInDate);
                    // countSlot();
                    if (checkInDate) {
                        $(".time_picker").prop("disabled", false);
                        totalDays = 1;
                        $("#reserve_btn").prop('disabled', false);
                    }
                }
            });

            // function countSlot() {
            //     var no_of_slots = $('.hourly_slots .select2-selection .select2-selection__rendered').find(
            //         '.select2-selection__choice').length;
            //     console.log('no_of_slots: ' + no_of_slots);
            //     if ($('.hourly_slots .select2-selection__rendered .slots_item').length == 0) {
            //         $('.hourly_slots .select2-selection__rendered').append(
            //             '<li class="slots_item"><span class="slot">' + no_of_slots + '</span> Slots</li>');
            //         $('#reserve_btn').attr('disabled', false);
            //     } else {
            //         $('.select2-selection__rendered .slots_item .slot').text(no_of_slots);
            //         if (no_of_slots == 0) {
            //             $('.hourly_slots .select2-selection__rendered .slots_item .slot').text('Select');
            //             $('#reserve_btn').attr('disabled', true);
            //         } else {
            //             $('#reserve_btn').attr('disabled', false);
            //         }
            //     }
            // }
            $(document).on("change", ".hourly_slots .multi_select", function () {
                let selectedSlots = $(this).val() ? $(this).val().length : 0;
                let $discountAmountEl = $(".nl-discount-amount");
                let $totalHoursEl = $(".total_hours");
                let $listingTotalEl = $(".listing_total_amount");
                let $totalAmountEl = $("#total_amount");
                let $reserveBtn = $("#reserve_btn");

                let discountAmount = 0;
                let listingTotalAmount = listing_price * selectedSlots;
                let totalAmount = listingTotalAmount;

                if (selectedSlots > 0) {
                    if (new_listing_discount?.discount_percentage) {
                        discountAmount = (listingTotalAmount * new_listing_discount.discount_percentage) / 100;
                        totalAmount -= discountAmount;
                    }

                    if(selectedSlots <= 1){
                        $(".hourly_slots .select2-selection__rendered").append(
                            `<li class="slots_item"><span class="slot">${selectedSlots}</span> {{ translate('listing_details.slot') }}</li>`
                        );
                    }else{
                        $(".hourly_slots .select2-selection__rendered").append(
                            `<li class="slots_item"><span class="slot">${selectedSlots}</span> {{ translate('listing_details.slots') }}</li>`
                        );
                    }

                    
                    $("#nl-discount-div").show();
                    $reserveBtn.prop("disabled", false);
                } else {
                    totalAmount = 0;
                    listingTotalAmount = 0;
                    $("#nl-discount-div").hide();
                    $reserveBtn.prop("disabled", true);
                }

                console.log({ selectedSlots, discountAmount, listingTotalAmount, totalAmount });
                

                // Update UI
                $discountAmountEl.html(`- ${discountAmount.toLocaleString('en')}`);
                $totalHoursEl.html(selectedSlots);
                $listingTotalEl.html(listingTotalAmount.toLocaleString('en'));
                $totalAmountEl.html(totalAmount.toLocaleString('en'));
            });
            // countSlot();
        });
    </script>
@endpush
