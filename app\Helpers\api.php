<?php

use Carbon\Carbon;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Aws\Translate\TranslateClient;

function api_response($status, $message, $data = null)
{
    return ["status" => $status, "message" => $message, "data" => $data];
}
function api_response_json(bool $status, string $message, mixed $data = null, int $code = 200)
{
    return response()->json(["status" => $status, "message" => $message, "data" => $data], $code);
}
function get_auth_token($login = "fc52c42e1817e90bde42839d5687a6f6", $tranKey = "3lkldtyiA863H76n")
{
    $nonce = random_bytes(16);
    $seed = date('c');
    $digest = base64_encode(hash('sha256', $nonce . $seed . $tranKey, true));
    return [
        'login' => $login,
        'tranKey' => $digest,
        'nonce' => base64_encode($nonce),
        'seed' => $seed,
    ];
}
function replacePhoneNumbers($text)
{
    $pattern = '/\b(1\s?)?(\(\d{3}\)|\d{3})?([-.\s]?\d{3}[-.\s]?\d{4}|\d{5,11})\b/';
    $replacedText = preg_replace($pattern, 'xxxxxxxxx', $text); // replace phone number with xxxxxxxx
    return $replacedText;
}
function admins_notification($notification_class)
{
    $admins = App\Models\User::get();
    foreach ($admins as $admin) {
        if ($admin->hasRole(['user', "sub_admin"]) || $admin->hasRole("sub_admin")) {
            $admin->notify($notification_class);
        }
    }
}
function google_map_key()
{
    return "AIzaSyAbi9IUF4TBu58oC9iGZexb045rMaQr2AQ";
    //return "AIzaSyD0ypp0864A7OFyrLPMOy60MyGA58M-eyc";
}
function skyflow_bearer_token()
{
    $creds = config("constant.skyflow_token_creds");
    $claims = [
        "iss" => $creds["clientID"],
        "key" => $creds["keyID"],
        "aud" => $creds["tokenURI"],
        "exp" => time() + 3600, // JWT expires in Now + 60 minutes
        "sub" => $creds["clientID"]
    ];
    $privateKey = $creds["privateKey"];
    $signedJWT = JWT::encode($claims, $privateKey, 'RS256');
    $body = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $signedJWT
    ];
    $response = Http::post($creds["tokenURI"], $body);
    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \Exception("Failed to obtain Bearer token: " . $response->body());
    }
}
function skyflow_acc_id()
{
    return config('constant.skyflow_acc_id');
}
function skyflow_workspace_id()
{
    return config('constant.skyflow_workspace_id');
}
function skyflow_vault_url()
{
    return config('constant.skyflow_vault_url');
}
function skyflow_vault_id()
{
    return config('constant.skyflow_vault_id');
}
function front_end_bearer_token()
{
    $creds = config("constant.frontend_token_creds");
    $claims = [
        "iss" => $creds["clientID"],
        "key" => $creds["keyID"],
        "aud" => $creds["tokenURI"],
        "exp" => time() + 3600, // JWT expires in Now + 60 minutes
        "sub" => $creds["clientID"]
    ];
    $privateKey = $creds["privateKey"];
    $signedJWT = JWT::encode($claims, $privateKey, 'RS256');
    $body = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $signedJWT
    ];
    $response = Http::post($creds["tokenURI"], $body);
    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \Exception("Failed to obtain Bearer token: " . $response->body());
    }
}
function skyflow_stripe_bearer_token()
{
    $creds = config("constant.skyflow_stripe_token_creds");
    $claims = [
        "iss" => $creds["clientID"],
        "key" => $creds["keyID"],
        "aud" => $creds["tokenURI"],
        "exp" => time() + 3600, // JWT expires in Now + 60 minutes
        "sub" => $creds["clientID"]
    ];
    $privateKey = $creds["privateKey"];
    $signedJWT = JWT::encode($claims, $privateKey, 'RS256');
    $body = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $signedJWT
    ];
    $response = Http::post($creds["tokenURI"], $body);
    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \Exception("Failed to obtain Bearer token: " . $response->body());
    }
}
function skyflow_payment_method($card, $cvc)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://dbfd4d880008.gateway.skyflowapis.com/v1/gateway/outboundRoutes/a97d1989c5664e4bbd898b7bc3fd8146/v1/payment_methods");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'type' => 'card',
        'card' => [
            'number' => $card["card_number"],
            'exp_month' => $card["expiry_date"],
            'exp_year' => $card["expiry_date"],
            'cvc' => $cvc,
        ]
    ]));
    $headers = [
        'X-Skyflow-Authorization: ' . skyflow_bearer_token()["accessToken"],
        'Authorization: Basic ' . base64_encode(secret_key()),
        'Content-Type: application/x-www-form-urlencoded'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo 'Error:' . curl_error($ch);
    }
    curl_close($ch);
    $response = json_decode($response, true);
    return $response;
}
function stripe_key()
{
    return config("constant.STRIPE_KEY");
}
function secret_key()
{
    return config("constant.STRIPE_SECRET");
}
function generate_otp($length = 6)
{
    $length = 6;
    $numeric = '123456789';
    $alpha = '123456789';
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $set = ($i % 2 == 0) ? $alpha : $numeric;
        $otp .= $set[random_int(0, strlen($set) - 1)];
    }
    return $otp;
}
// function check_listing_price($listing_id, $total_amount)
// {
//     $listing = App\Listing::find($listing_id);
//     if ($listing) {
//         $price = 0;
//         switch ($listing->detail->basis_type) {
//             case 'Tour':
//                 $price = $listing->detail->adult_price;
//                 break;
//             case 'Hourly':
//                 $price = $listing->detail->per_hour;
//                 break;
//             case 'Daily':
//                 $price = $listing->detail->per_day;
//                 break;
//         }
//         if ($total_amount < $price) {
//             return false;
//             // return api_response(false, "Total Amount is not valid");
//         } else {
//             return true;
//         }
//     }
// }
function common_setting()
{
    return App\CommonSetting::first();
}
function get_percentage($discount_percentage, $total_amount)
{
    $discount_value = ($total_amount * $discount_percentage) / 100;
    return $discount_value;
}
function base_price($listing_detail)
{
    if ($listing_detail["basis_type"] == "Daily") {
        return $listing_detail['per_day'];
    } elseif ($listing_detail["basis_type"] == "Hourly") {
        return $listing_detail['per_hour'];
    } else {
        return $listing_detail['adult_price'];
    }
}
function get_paypal_percentage($discount_percentage, $total_amount)
{
    $discount_value = ($total_amount * $discount_percentage) / 100;
    return $discount_value;
}
function weeklyMonthlyDiscount($totalDays, $total_amount, $discount)
{
    $discount_name = null;
    $discount_percentage = null;
    $discount_amount = null;
    $weekly_days = 7;
    $monthly_days = 28;
    $weekly_discount_percentage = $discount['weekly_discount'] ?? 0;
    $monthly_discount_percentage = $discount['monthly_discount'] ?? 0;
    if ($totalDays >= $weekly_days && $totalDays < $monthly_days && $weekly_discount_percentage != 0) {
        $weekly_discount = get_percentage($total_amount, $weekly_discount_percentage);
        $total_amount -= $weekly_discount;
        $discount_amount = $weekly_discount;
        $discount_name = "Weekly Discount";
        $discount_percentage = $weekly_discount_percentage;
    } elseif ($totalDays >= $monthly_days && $monthly_discount_percentage != 0) {
        $monthly_discount = get_percentage($total_amount, $monthly_discount_percentage);
        $discount_amount = $monthly_discount;
        $total_amount -= $monthly_discount;
        $discount_name = "Monthly Discount";
        $discount_percentage = $monthly_discount_percentage;
    }
    return [
        'total_amount' => $total_amount,
        'discount_name' => $discount_name,
        'discount_percentage' => $discount_percentage,
        "discount_amount" => $discount_amount
    ];
}
function newListingDiscount($total_amount, $listing_booking_count, $discount)
{
    $discount_name = null;
    $discount_percentage = null;
    $discount_amount = null;
    $new_listing_discount_percentage = $discount['new_listing_discount'] ?? null;
    if (isset($new_listing_discount_percentage) && $listing_booking_count < 3) {
        $new_listing_discount = get_percentage($total_amount, $new_listing_discount_percentage);
        $discount_amount = $new_listing_discount;
        $total_amount -= $new_listing_discount;
        $discount_name = "New Listing Discount";
        $discount_percentage = $new_listing_discount_percentage;
    }
    return [
        'total_amount' => $total_amount,
        'discount_name' => $discount_name,
        'discount_percentage' => $discount_percentage,
        "discount_amount" => $discount_amount
    ];
}
function season_price($start_date, $base_price, $seasons)
{
    $listingPrice = $base_price;
    $today = Carbon::parse($start_date)->format('m/d/Y');
    foreach ($seasons as $season) {
        if ($today >= $season['start_date'] && $today <= $season['end_date']) {
            if ($season['type'] === 'Increase') {
                $listingPrice += ($listingPrice * $season['percentage'] / 100);
            } elseif ($season['type'] === 'Decrease') {
                $listingPrice -= ($listingPrice * $season['percentage'] / 100);
            }
        }
    }
    return $listingPrice;
}
// Function to fetch translation based on the key
function translate($key, $replace = [])
{
    $locale = app()->getLocale(); // Get the current locale (e.g., 'en', 'fr', 'es')
    $path = resource_path("lang/{$locale}.json"); // Path to the language JSON file
    if (file_exists($path)) {
        // Read the JSON file and decode it into an array
        $translations = json_decode(file_get_contents($path), true);
        $translation = data_get($translations, $key, $key);  // Return the translation or the key if not found
        // Replace placeholders if any
        if (!empty($replace) && is_array($replace)) {
            foreach ($replace as $search => $replacement) {
                $translation = str_replace(':' . $search, $replacement, $translation);
            }
        }
        return $translation;
    }
    return $key; // Return the key if translation file is missing
}
function translateNotification($notificationData, $locale = null)
{
    $locale = $locale ?: app()->getLocale();

    // If notification already has template_key, use it
    if (isset($notificationData['template_key'])) {
        if (!isset($notificationData['locale']) || $notificationData['locale'] === $locale) {
            return $notificationData;
        }

        $template = \App\Models\NotificationTemplate::where('key', $notificationData['template_key'])->first();
        if ($template) {
            $notificationData['title'] = $template->getTranslation($locale, 'title');
            $notificationData['message'] = $template->getTranslation($locale, 'message');
        }
        return $notificationData;
    }

    // Map existing notification titles to template keys
    $titleToKeyMap = [
        'Booking placed' => 'booking_placed',
        'Booking Cancelled' => 'booking_cancelled',
        'New User Registered' => 'user_registered',
        'Reserva Realizada' => 'booking_placed',
        'Reserva Cancelada' => 'booking_cancelled',
        'Nuevo Usuario Registrado' => 'user_registered'
    ];

    $title = $notificationData['title'] ?? '';
    $templateKey = $titleToKeyMap[$title] ?? null;

    if (!$templateKey) {
        return $notificationData; // Return as-is if no mapping found
    }

    $template = \App\Models\NotificationTemplate::where('key', $templateKey)->first();
    if (!$template) {
        return $notificationData;
    }

    // Get translated template
    $translatedTitle = $template->getTranslation($locale, 'title');
    $translatedMessage = $template->getTranslation($locale, 'message');

    // Extract data from existing message for placeholder replacement
    $message = $notificationData['message'] ?? '';
    $extractedData = extractNotificationData($message, $templateKey);
    // Replace placeholders
    foreach ($extractedData as $placeholder => $value) {
        $translatedMessage = str_replace($placeholder, $value, $translatedMessage);
    }

    return [
        'title' => $translatedTitle,
        'message' => $translatedMessage,
        'locale' => $locale,
        'template_key' => $templateKey
    ];
}
function getNotificationPatterns()
{
    return [
        'booking_placed' => [
            'patterns' => [
                '/<b>([^<]+)<\/b> booked the <b>([^<]+)<\/b> on data: ([^"]+)/',
                '/^([^<]+) booked the ([^<]+) on data: ([^"]+)/',
                '/^([^<]+) booked the (.+) on data: ([^"]+)$/',
            ],
            'placeholders' => [':customer_name', ':listing_name', ':check_in_date']
        ],
        'user_registered' => [
            'patterns' => [
                '/New Registration (.+) has been registered/',
                '/Congratulations! (.+) your account has been registered/',
                '/([A-Z][a-z]+ [A-Z][a-z]+)/',
            ],
            'placeholders' => [':user_name'],
            'fallback' => [':user_name' => 'User']
        ],
        'booking_cancelled' => [
            'patterns' => [
                '/([^<]+) cancelled booking for (.+) on (.+)/',
                '/<b>([^<]+)<\/b> cancelled booking for <b>([^<]+)<\/b> on (.+)/',
            ],
            'placeholders' => [':customer_name', ':listing_name', ':check_in_date']
        ]
    ];
}
function extractNotificationData($message, $templateKey)
{
    $data = [];

    switch ($templateKey) {
        case 'booking_placed':
            $patterns = [
                // Pattern 1: "<b>Eve Noble</b> booked the <b>Tafsol Housing Society</b> on data: 2024-01-04"
                '/<b>([^<]+)<\/b> booked the <b>([^<]+)<\/b> on data: ([^"]+)/',
                // Pattern 2: "Admin booked the Testing House on data: 2024-05-08"
                '/^([^<]+) booked the ([^<]+) on data: ([^"]+)/',
                // Pattern 3: "Fray Forbes booked the Best fishing experience in Cartagena on data: 2025-04-10"
                '/^([^<]+) booked the (.+) on data: ([^"]+)$/',
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $message, $matches)) {
                    $data[':customer_name'] = trim($matches[1]);
                    $data[':listing_name'] = trim($matches[2]);
                    $data[':check_in_date'] = trim($matches[3]);
                    break;
                }
            }
            break;

        case 'user_registered':
            $patterns = [
                // Pattern 1: "New Registration Chris Mckenzie Blur has been registered"
                '/New Registration (.+) has been registered/',
                // Pattern 2: "Congratulations! John Doe your account has been registered"
                '/Congratulations! (.+) your account has been registered/',
                // Pattern 3: Extract name from any message containing a name
                '/([A-Z][a-z]+ [A-Z][a-z]+)/',
            ];
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $message, $matches)) {
                    $data[':user_name'] = trim($matches[1]);
                    break;
                }
            }
            // Fallback if no name found
            if (empty($data[':user_name'])) {
                $data[':user_name'] = 'User';
            }
            break;
        case 'booking_cancelled':
            $patterns = [
                // Add patterns for booking cancelled
                '/([^<]+) cancelled booking for (.+) on (.+)/',
                '/<b>([^<]+)<\/b> cancelled booking for <b>([^<]+)<\/b> on (.+)/',
            ];
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $message, $matches)) {
                    $data[':customer_name'] = trim($matches[1]);
                    $data[':listing_name'] = trim($matches[2]);
                    $data[':check_in_date'] = trim($matches[3]);
                    break;
                }
            }
            break;
    }
    return $data;
}
//Amazon translate api start
// function translateDynamic($text, $targetLanguage = null)
// {
//     if (empty($text)) {
//         //logger('translateDynamic: Empty text provided');
//         return $text;
//     }
//     $targetLanguage = $targetLanguage ?: app()->getLocale();
//     //logger("translateDynamic: Input - Text: '$text', Target: '$targetLanguage', Current Locale: " . app()->getLocale());
//     // If target language is English, return original text
//     if ($targetLanguage === 'en') {
//         //logger('translateDynamic: Target is English, returning original text');
//         return $text;
//     }
//     try {
//         // Create a TranslateClient instance
//         $translateClient = new TranslateClient([
//             'version' => 'latest',
//             'region'  => config('services.aws.region'),
//             'credentials' => [
//                 'key'    => config('services.aws.key'),
//                 'secret' => config('services.aws.secret'),
//             ]
//         ]);
//         //logger('translateDynamic: AWS Client created successfully');
//         // Language mapping for AWS Translate
//         $languageMap = [
//             'es' => 'es',
//             'fr' => 'fr',
//             'de' => 'de',
//             'it' => 'it',
//             'pt' => 'pt',
//             'ru' => 'ru',
//             'ja' => 'ja',
//             'zh' => 'zh',
//             'ko' => 'ko',
//             'ar' => 'ar',
//             'hi' => 'hi',
//             'tr' => 'tr',
//             'nl' => 'nl',
//             'pl' => 'pl'
//         ];
//         $mappedLanguage = $languageMap[strtolower($targetLanguage)] ?? $targetLanguage;
//         //logger("translateDynamic: Language mapping - Original: '$targetLanguage', Mapped: '$mappedLanguage'");
//         // Translate the text using Amazon Translate
//         $result = $translateClient->translateText([
//             'Text' => $text,
//             'SourceLanguageCode' => 'auto',
//             'TargetLanguageCode' => $mappedLanguage,
//         ]);
//         //logger('translateDynamic: AWS Response received: ' . json_encode($result->toArray()));
//         if (isset($result['TranslatedText'])) {
//             $translatedText = $result['TranslatedText'];
//             //logger("translateDynamic: SUCCESS - Original: '$text' -> Translated: '$translatedText'");
//             return $translatedText;
//         } else {
//             //logger("translateDynamic: ERROR - No TranslatedText in response: " . json_encode($result->toArray()));
//         }
//         return $text;
//     } catch (\Aws\Exception\AwsException $e) {
//         //logger('translateDynamic: AWS Exception - Code: ' . $e->getAwsErrorCode() . ', Message: ' . $e->getMessage());
//         return $text;
//     } catch (Exception $e) {
//         //logger('translateDynamic: General Exception: ' . $e->getMessage());
//         return $text;
//     }
// }
//Amazon translate function end

//Deep l translate function
// function translateDynamic($text, $targetLanguage = null)
// {
//     if (empty($text)) {
//         return $text;
//     }

//     $targetLanguage = $targetLanguage ?: app()->getLocale();

//     // If target language is English, return original text
//     if ($targetLanguage === 'en') {
//         return $text;
//     }

//     try {
//         // DeepL API v2 endpoint (most current and accurate)
//         $url = 'https://api-free.deepl.com/v2/translate';

//         // Get API key from config (more secure)
//         $apiKey = config('services.deepl.key', 'af80c7d8-14c2-446d-ba32-5e5412db7c9f:fx');

//         // Map locale codes to DeepL supported languages
//         $languageMap = [
//             'es' => 'ES',
//             'en' => 'EN',
//             'fr' => 'FR',
//             'de' => 'DE',
//             'it' => 'IT',
//             'pt' => 'PT',
//             'ru' => 'RU',
//             'ja' => 'JA',
//             'zh' => 'ZH',
//             'ko' => 'KO',
//             'nl' => 'NL',
//             'pl' => 'PL',
//             'sv' => 'SV',
//             'da' => 'DA',
//             'fi' => 'FI',
//             'no' => 'NB',
//             'cs' => 'CS',
//             'sk' => 'SK',
//             'sl' => 'SL',
//             'et' => 'ET',
//             'lv' => 'LV',
//             'lt' => 'LT',
//             'bg' => 'BG',
//             'hu' => 'HU',
//             'ro' => 'RO',
//             'el' => 'EL',
//             'tr' => 'TR',
//             'uk' => 'UK',
//             'ar' => 'AR',
//             'hi' => 'HI',
//             'id' => 'ID',
//             'th' => 'TH',
//             'vi' => 'VI'
//         ];

//         $targetLang = $languageMap[strtolower($targetLanguage)] ?? strtoupper($targetLanguage);

//         $data = [
//             'text' => $text,
//             'target_lang' => $targetLang,
//             'auth_key' => $apiKey,
//             'preserve_formatting' => '1', // Preserve HTML formatting
//             'formality' => 'default', // Use default formality level
//             'split_sentences' => '1' // Split sentences for better translation
//         ];

//         $ch = curl_init();
//         curl_setopt($ch, CURLOPT_URL, $url);
//         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//         curl_setopt($ch, CURLOPT_POST, true);
//         curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
//         curl_setopt($ch, CURLOPT_HTTPHEADER, [
//             'Content-Type: application/x-www-form-urlencoded',
//             'User-Agent: Laravel-DeepL-Client/1.0'
//         ]);
//         curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Increased timeout
//         curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

//         $response = curl_exec($ch);
//         $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//         $curlError = curl_error($ch);
//         curl_close($ch);

//         // Handle cURL errors
//         if ($curlError) {
//             logger("translateDynamic: cURL error: $curlError");
//             return $text;
//         }

//         // Handle HTTP errors with specific messages
//         if ($httpCode !== 200) {
//             $errorMessages = [
//                 400 => 'Bad request - check parameters',
//                 403 => 'Forbidden - check API key',
//                 413 => 'Request too large',
//                 429 => 'Too many requests - rate limited',
//                 456 => 'Quota exceeded',
//                 503 => 'Resource temporarily unavailable'
//             ];

//             $errorMsg = $errorMessages[$httpCode] ?? "HTTP error code: $httpCode";
//             logger("translateDynamic: $errorMsg, Response: $response");
//             return $text;
//         }

//         if ($response) {
//             $result = json_decode($response, true);

//             if (json_last_error() !== JSON_ERROR_NONE) {
//                 logger("translateDynamic: JSON decode error: " . json_last_error_msg());
//                 return $text;
//             }

//             if (isset($result['translations'][0]['text'])) {
//                 $translatedText = $result['translations'][0]['text'];

//                 // Log successful translation for debugging
//                 logger("translateDynamic: Successfully translated '$text' to '$translatedText'");

//                 return $translatedText;
//             } else {
//                 logger("translateDynamic: No translation found in response: " . json_encode($result));
//             }
//         }

//         return $text;

//     } catch (Exception $e) {
//         logger('translateDynamic: Exception occurred: ' . $e->getMessage());
//         return $text;
//     }
// }
//google free translate api function
// function translateDynamic($text, $targetLanguage = null)
// {
//     if (empty($text)) {
//         //logger('translateDynamic: Empty text provided');
//         return $text;
//     }
//     $targetLanguage = $targetLanguage ?: app()->getLocale();
//     //logger('translateDynamic: Text=' . $text . ', Target=' . $targetLanguage . ', Current Locale=' . app()->getLocale());
//     // If target language is English or same as source, return original text
//     if ($targetLanguage === 'en') {
//         //logger('translateDynamic: Target is English, returning original text');
//         return $text;
//     }
//     try {
//         // Use free Google Translate endpoint (same as your MERN code)
//         $url = 'https://translate.googleapis.com/translate_a/single';
//         $params = [
//             'client' => 'gtx',
//             'sl' => 'auto',
//             'tl' => $targetLanguage,
//             'dt' => 't',
//             'q' => $text
//         ];
//         $queryString = http_build_query($params);
//         $fullUrl = $url . '?' . $queryString;
//         //logger('translateDynamic: Making request to: ' . $fullUrl);
//         $ch = curl_init();
//         curl_setopt($ch, CURLOPT_URL, $fullUrl);
//         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//         curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
//         curl_setopt($ch, CURLOPT_TIMEOUT, 10);

//         $response = curl_exec($ch);
//         $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//         curl_close($ch);
//         //logger('translateDynamic: HTTP Code=' . $httpCode . ', Response=' . substr($response, 0, 500));
//         if ($httpCode === 200 && $response) {
//             $result = json_decode($response, true);
//             //logger('translateDynamic: Decoded result', $result);
//             if (isset($result[0]) && is_array($result[0])) {
//                 $translatedTexts = [];
//                 foreach ($result[0] as $translation) {
//                     if (isset($translation[0])) {
//                         $translatedTexts[] = $translation[0];
//                     }
//                 }
//                 $translatedText = implode(' ', $translatedTexts);
//                 //logger('translateDynamic: Translation successful: ' . $translatedText);
//                 return $translatedText ?: $text;
//             } else {
//                 //logger('translateDynamic: No translation found in response structure');
//             }
//         } else {
//             //logger('translateDynamic: HTTP error code: ' . $httpCode);
//         }

//         return $text;
//     } catch (Exception $e) {
//         //logger('translateDynamic: Exception occurred: ' . $e->getMessage());
//         return $text;
//     }
// }
function gcp_base64url($data)
{
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}
function gcp_jwt_sign(array $header, array $claims, string $privateKey)
{
    $segments = [gcp_base64url(json_encode($header)), gcp_base64url(json_encode($claims))];
    $input = implode('.', $segments);
    openssl_sign($input, $sig, $privateKey, 'sha256');
    return $input . '.' . gcp_base64url($sig);
}
function gcp_access_token(): string
{
    return Cache::remember('gcp:token', 50 * 60, function () {
        $saPath = env('GOOGLE_SA_KEY_PATH');
        if (!$saPath || !file_exists($saPath)) {
            throw new \RuntimeException('Google service account key file not found');
        }

        $sa = json_decode(file_get_contents($saPath), true);
        $now = time();
        $header = ['alg' => 'RS256', 'typ' => 'JWT'];
        $claims = [
            'iss'   => $sa['client_email'],
            'scope' => 'https://www.googleapis.com/auth/cloud-translation',
            'aud'   => 'https://oauth2.googleapis.com/token',
            'iat'   => $now,
            'exp'   => $now + 3600,
        ];
        $jwt = gcp_jwt_sign($header, $claims, $sa['private_key']);
        $ch = curl_init('https://oauth2.googleapis.com/token');
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query([
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion'  => $jwt,
            ]),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
        ]);
        $res = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($code !== 200) {
            logger('GCP token error: ' . $code . ' ' . $res);
            throw new \RuntimeException('Token error: ' . $code . ' ' . $res);
        }
        $json = json_decode($res, true);
        return $json['access_token'] ?? throw new \RuntimeException('No access_token');
    });
}
if (!function_exists('formatLocalizedDate')) {
    function formatLocalizedDate($date, $format = 'd M Y')
    {
        if (empty($date) || $date === '-') {
            return '-';
        }
        try {
            $carbonDate = \Carbon\Carbon::parse($date);
            $locale = app()->getLocale();
            $carbonDate->locale($locale);
            $formattedDate = $carbonDate->translatedFormat($format);
            // Capitalize first letter of month and remove dots
            if ($locale === 'es') {
                $formattedDate = str_replace('.', '', $formattedDate);
            }
            // Capitalize first letter of the month part
            $parts = explode(' ', $formattedDate);
            if (count($parts) >= 2) {
                $parts[1] = ucfirst($parts[1]); // Capitalize month
                $formattedDate = implode(' ', $parts);
            }

            return $formattedDate;
        } catch (Exception $e) {
            return $date;
        }
    }
}
if (!function_exists('formatLocalizedDateTime')) {
    function formatLocalizedDateTime($dateTime, $format = 'd M Y H:i:s')
    {
        if (empty($dateTime) || $dateTime === '-') {
            return '-';
        }
        
        try {
            $carbonDate = \Carbon\Carbon::parse($dateTime);
            $locale = app()->getLocale();
            $carbonDate->locale($locale);
            
            // Split format into date and time parts
            $datePart = 'd M Y';
            $timePart = 'H:i:s';
            
            // Format date part with translation
            $formattedDate = $carbonDate->translatedFormat($datePart);
            
            // Format time part (no translation needed)
            $formattedTime = $carbonDate->format($timePart);
            
            // Handle Spanish locale formatting
            if ($locale === 'es') {
                $formattedDate = str_replace('.', '', $formattedDate);
            }
            
            // Capitalize first letter of the month part
            $dateParts = explode(' ', $formattedDate);
            if (count($dateParts) >= 2) {
                $dateParts[1] = ucfirst($dateParts[1]); // Capitalize month
                $formattedDate = implode(' ', $dateParts);
            }
            
            return $formattedDate . ' ' . $formattedTime;
            
        } catch (Exception $e) {
            return $dateTime;
        }
    }
}
//Without cache functionality
// function translateDynamic($text, $targetLanguage = null)
// {
//     if (empty($text)) {
//         //logger('translateDynamicModernMT: Empty text provided');
//         return $text;
//     }
//     $targetLanguage = $targetLanguage ?: app()->getLocale();
//     //logger("translateDynamicModernMT: Input - Text: '$text', Target: '$targetLanguage'");
//     if ($targetLanguage === 'en') {
//         //logger('translateDynamicModernMT: Target is English, returning original text');
//         return $text;
//     }
//     try {
//         $apiKey = '9814B6A0-2B86-6160-AAAB-A3CF6FF47360';
//         $baseUrl = 'https://api.modernmt.com/translate';   
//         $languageMap = [
//             'es' => 'es', 'es-mx' => 'es', 'fr' => 'fr', 'de' => 'de', 'it' => 'it', 'pt' => 'pt',
//             'ru' => 'ru', 'ja' => 'ja', 'zh' => 'zh', 'ko' => 'ko', 'ar' => 'ar',
//             'hi' => 'hi', 'tr' => 'tr', 'nl' => 'nl', 'pl' => 'pl'
//         ];
//         $mappedLanguage = $languageMap[strtolower($targetLanguage)] ?? $targetLanguage;
//         //logger("translateDynamicModernMT: Language mapping - Original: '$targetLanguage', Mapped: '$mappedLanguage'");
//         //logger('translateDynamicModernMT: Making ModernMT API request');
//         // Use cURL directly (matching your working Postman request)
//         $curl = curl_init();
//         $postData = json_encode([
//             'source' => 'en',
//             'target' => $mappedLanguage=='es'?'es-mx':$mappedLanguage,
//             'q' => $text
//         ]);
        
//         curl_setopt_array($curl, [
//             CURLOPT_URL => $baseUrl,
//             CURLOPT_RETURNTRANSFER => true,
//             CURLOPT_ENCODING => '',
//             CURLOPT_MAXREDIRS => 10,
//             CURLOPT_TIMEOUT => 30,
//             CURLOPT_FOLLOWLOCATION => true,
//             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//             CURLOPT_CUSTOMREQUEST => 'POST',
//             CURLOPT_POSTFIELDS => $postData,
//             CURLOPT_HTTPHEADER => [
//                 'X-HTTP-Method-Override: GET',
//                 'Content-Type: application/json',
//                 'MMT-ApiKey: ' . $apiKey
//             ],
//         ]);
        
//         $response = curl_exec($curl);
//         $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
//         $curlError = curl_error($curl);
//         curl_close($curl);
//         //logger('translateDynamicModernMT: Response Status: ' . $httpCode);
//         //logger('translateDynamicModernMT: Response Body: ' . $response);
//         if ($curlError) {
//             //logger('translateDynamicModernMT: cURL Error: ' . $curlError);
//             return $text;
//         }
//         if ($httpCode === 200 && $response) {
//             $result = json_decode($response, true);
            
//             if (isset($result['data']['translation'])) {
//                 $translatedText = $result['data']['translation'];
//                 //logger("translateDynamicModernMT: SUCCESS - Original: '$text' -> Translated: '$translatedText'");
//                 return $translatedText;
//             } else {
//                 //logger('translateDynamicModernMT: No translation in response: ' . json_encode($result));
//             }
//         } else {
//             //logger('translateDynamicModernMT: API failed - Status: ' . $httpCode . ', Body: ' . $response);
//         }
//         return $text;
//     } catch (Exception $e) {
//         //logger('translateDynamicModernMT: Exception: ' . $e->getMessage());
//         return $text;
//     }
// }
//End without Cache functionality
// function translateDynamic($text, $targetLanguage = null)
// {
//     if (empty($text)) {
//         return $text;
//     }
//     $targetLanguage = $targetLanguage ?: app()->getLocale();
//     if ($targetLanguage === 'en') {
//         return $text;
//     }
//     $cacheKey = 'translation_' . md5($text . '_' . $targetLanguage);
//     $translationCache = Cache::store('translations');
//     $cachedTranslation = $translationCache->get($cacheKey);
//     if ($cachedTranslation !== null) {
//         // logger("Translation CACHE HIT: '{$text}' -> '{$cachedTranslation}' (lang: {$targetLanguage})");
//         return $cachedTranslation;
//     }
//     // Log API request
//     // logger("Translation API REQUEST: '{$text}' (lang: {$targetLanguage})");
//     try {
//         $apiKey = '9814B6A0-2B86-6160-AAAB-A3CF6FF47360';
//         $baseUrl = 'https://api.modernmt.com/translate';   
//         $languageMap = [
//             'es' => 'es', 'es-mx' => 'es', 'fr' => 'fr', 'de' => 'de', 'it' => 'it', 'pt' => 'pt',
//             'ru' => 'ru', 'ja' => 'ja', 'zh' => 'zh', 'ko' => 'ko', 'ar' => 'ar',
//             'hi' => 'hi', 'tr' => 'tr', 'nl' => 'nl', 'pl' => 'pl'
//         ];
//         $mappedLanguage = $languageMap[strtolower($targetLanguage)] ?? $targetLanguage;
//         $curl = curl_init();
//         $postData = json_encode([
//             'source' => 'en',
//             'target' => $mappedLanguage == 'es' ? 'es-mx' : $mappedLanguage,
//             'q' => $text
//         ]);
//         curl_setopt_array($curl, [
//             CURLOPT_URL => $baseUrl,
//             CURLOPT_RETURNTRANSFER => true,
//             CURLOPT_ENCODING => '',
//             CURLOPT_MAXREDIRS => 10,
//             CURLOPT_TIMEOUT => 30,
//             CURLOPT_FOLLOWLOCATION => true,
//             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//             CURLOPT_CUSTOMREQUEST => 'POST',
//             CURLOPT_POSTFIELDS => $postData,
//             CURLOPT_HTTPHEADER => [
//                 'X-HTTP-Method-Override: GET',
//                 'Content-Type: application/json',
//                 'MMT-ApiKey: ' . $apiKey
//             ],
//         ]);
//         $response = curl_exec($curl);
//         $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
//         $curlError = curl_error($curl);
//         curl_close($curl);
//         if ($curlError) {
//             // logger("Translation API ERROR: cURL error - {$curlError}");
//             $translationCache->put($cacheKey, $text, now()->addDays(30));
//             return $text;
//         }
//         if ($httpCode === 200 && $response) {
//             $result = json_decode($response, true);   
//             if (isset($result['data']['translation'])) {
//                 $translatedText = $result['data']['translation'];
//                 // Cache in dedicated translation store for 30 days
//                 $translationCache->put($cacheKey, $translatedText, now()->addDays(30));
//                 // logger("Translation API SUCCESS: '{$text}' -> '{$translatedText}' (lang: {$targetLanguage})");
//                 return $translatedText;
//             }
//         }
//         // logger("Translation API FAILED: HTTP {$httpCode} - {$response}");
//         $translationCache->put($cacheKey, $text, now()->addDays(30));
//         return $text;
//     } catch (Exception $e) {
//         // logger("Translation EXCEPTION: " . $e->getMessage());
//         $translationCache->put($cacheKey, $text, now()->addDays(30));
//         return $text;
//     }
// }

function translateDynamic($text, $targetLanguage = null, $originalLocale = null)
{
    logger("translateDynamic called with: text='{$text}', target='{$targetLanguage}', original='{$originalLocale}'");

    if (empty($text) || is_numeric($text)) {
        return $text;
    }
    $targetLanguage = $targetLanguage ?: app()->getLocale();
    if ($originalLocale && $originalLocale === $targetLanguage) {
        logger("translateDynamic: Original locale ({$originalLocale}) matches target ({$targetLanguage}), returning original text");
        return $text;
    }
    $cacheKey = 'translation_' . md5($text . '_' . $targetLanguage);
    $translationCache = Cache::store('translations');
    $cachedTranslation = $translationCache->get($cacheKey);
    if ($cachedTranslation !== null) {
        logger("translateDynamic: Found cached translation: '{$text}' -> '{$cachedTranslation}'");
        return $cachedTranslation;
    }
    logger("translateDynamic: No cache found, making API call to translate: '{$text}' to '{$targetLanguage}'");
    try {
        $apiKey = '9814B6A0-2B86-6160-AAAB-A3CF6FF47360';
        $baseUrl = 'https://api.modernmt.com/translate';
        $sourceLanguage = $originalLocale ?: detectLanguageModernMT($text);
        logger("translateDynamic: Using source language: '{$sourceLanguage}' for text: '{$text}'");
        if ($sourceLanguage === $targetLanguage) {
            logger("translateDynamic: Source and target languages are the same ({$sourceLanguage}), returning original text");
            $translationCache->put($cacheKey, $text, now()->addDays(30));
            return $text;
        }
        $languageMap = [
            'es' => 'es', 'es-mx' => 'es', 'fr' => 'fr', 'de' => 'de', 'it' => 'it', 'pt' => 'pt',
            'ru' => 'ru', 'ja' => 'ja', 'zh' => 'zh', 'ko' => 'ko', 'ar' => 'ar',
            'hi' => 'hi', 'tr' => 'tr', 'nl' => 'nl', 'pl' => 'pl', 'en' => 'en'
        ];
        $mappedSourceLang = $languageMap[strtolower($sourceLanguage)] ?? $sourceLanguage;
        $mappedTargetLang = $languageMap[strtolower($targetLanguage)] ?? $targetLanguage;
        logger("translateDynamic: Mapped languages - source: {$mappedSourceLang}, target: {$mappedTargetLang}");
        $queryParams = http_build_query([
            'source' => $mappedSourceLang,
            'target' => $mappedTargetLang,
            'q' => $text
        ]);
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $baseUrl . '?' . $queryParams,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
             CURLOPT_HTTPHEADER => [
                'X-HTTP-Method-Override: GET',
                'Content-Type: application/json',
                'MMT-ApiKey: ' . $apiKey
            ],
        ]);
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);
        logger("translateDynamic: API call - URL: {$baseUrl}?{$queryParams}");
        logger("translateDynamic: API response - HTTP: {$httpCode}, Response: {$response}");
        if ($curlError) {
            logger("translateDynamic: cURL error - {$curlError}");
            $translationCache->put($cacheKey, $text, now()->addDays(30));
            return $text;
        }
        if ($httpCode === 200 && $response) {
            $result = json_decode($response, true);
            logger("translateDynamic: Parsed response: " . json_encode($result));   
            if (isset($result['data']['translation'])) {
                $translatedText = $result['data']['translation'];
                $translationCache->put($cacheKey, $translatedText, now()->addDays(30));
                logger("translateDynamic: SUCCESS - '{$text}' -> '{$translatedText}' ({$sourceLanguage} -> {$targetLanguage})");
                return $translatedText;
            }
        }
        logger("translateDynamic: API call failed - HTTP {$httpCode}, Response: {$response}");
        $translationCache->put($cacheKey, $text, now()->addDays(30));
        return $text;
        
    } catch (Exception $e) {
        logger("translateDynamic: Exception - " . $e->getMessage());
        $translationCache->put($cacheKey, $text, now()->addDays(30));
        return $text;
    }
}

function isEnglishContent($text)
{
    if (empty($text) || is_numeric($text)) {
        return true;
    }
    $spanishChars = ['á', 'é', 'í', 'ó', 'ú', 'ñ', 'ü'];
    foreach ($spanishChars as $char) {
        if (stripos($text, $char) !== false) {
            return false;
        }
    }
    return true;
}
function translateToEnglish($text)
{
    if (empty($text) || is_numeric($text)) {
        return $text;
    }
    $detectedLanguage = 'es';
    if ($detectedLanguage === 'en') {
        return $text;
    }
    if ($detectedLanguage !== 'en') {
        logger("Observer: Detected '{$detectedLanguage}' language, translating to English: '{$text}'");
        return translateDynamic($text, 'en');
    }
    return $text;
}

// Language detection using ModernMT API
function detectLanguageModernMT($text)
{
    if (empty($text) || is_numeric($text)) {
        return 'en';
    }
    $cacheKey = 'lang_detect_' . md5($text);
    $detectionCache = Cache::store('translations');
    $cachedLanguage = $detectionCache->get($cacheKey);
    if ($cachedLanguage !== null) {
        return $cachedLanguage;
    }
    try {
        $apiKey = '9814B6A0-2B86-6160-AAAB-A3CF6FF47360';
        $url = 'https://api.modernmt.com/translate/detect';
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode(['q' => $text]),
            CURLOPT_HTTPHEADER => [
                'X-HTTP-Method-Override: GET',
                'Content-Type: application/json',
                'MMT-ApiKey: ' . $apiKey
            ],
        ]);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);
        logger("Language detection API call: URL={$url}, HTTP={$httpCode}, Response={$response}");
        if ($curlError) {
            logger("Language detection cURL error: {$curlError}");
            return isEnglishContentFallback($text) ? 'en' : 'es';
        }
        if ($httpCode === 200 && $response) {
            $result = json_decode($response, true);
            if (isset($result['data']['detectedLanguage'])) {
                $detectedLang = $result['data']['detectedLanguage'];
                $detectionCache->put($cacheKey, $detectedLang, now()->addDays(7));
                logger("Language detection SUCCESS: '{$text}' -> '{$detectedLang}'");
                return $detectedLang;
            }
        }
        logger("Language detection FAILED: HTTP {$httpCode} - {$response}");
        
    } catch (Exception $e) {
        logger("Language detection EXCEPTION: " . $e->getMessage());
    }
    return isEnglishContentFallback($text) ? 'en' : 'es';
}
function isEnglishContentFallback($text)
{
    if (empty($text)) return true;
    $spanishChars = ['á', 'é', 'í', 'ó', 'ú', 'ñ', 'ü'];
    $spanishWords = ['el', 'la', 'de', 'que', 'y', 'en', 'por', 'con', 'es', 'un', 'una', 'hola', 'como', 'está'];
    $text = strtolower($text);
    foreach ($spanishChars as $char) {
        if (strpos($text, $char) !== false) {
            return false;
        }
    }
    foreach ($spanishWords as $word) {
        if (strpos($text, $word) !== false) {
            return false; // Contains Spanish words
        }
    }
    return true; // Assume English
}

if (!function_exists('shouldShowTranslateButton')) {
    function shouldShowTranslateButton($text, $currentLocale = null)
    {
        if (empty($text)) {
            return false;
        }
        
        $currentLocale = $currentLocale ?? app()->getLocale();
        $detectedLang = detectLanguageModernMT($text);
        
        // Only show translate button if detected language is different from current locale
        return $detectedLang !== $currentLocale;
    }
}

if (!function_exists('getTranslateButtonText')) {
    function getTranslateButtonText($text, $currentLocale = null)
    {
        if (empty($text)) {
            return '';
        }
        
        $currentLocale = $currentLocale ?? app()->getLocale();
        $detectedLang = detectLanguageModernMT($text);
        
        // If user is browsing in Spanish and content is English -> show "Translate to Spanish"
        if ($currentLocale === 'es' && $detectedLang === 'en') {
            return translate('listing_details.translate_to_spanish');
        } 
        // If user is browsing in English and content is Spanish -> show "Translate to English"
        elseif ($currentLocale === 'en' && $detectedLang === 'es') {
            return translate('listing_details.translate_to_english');
        }
        
        // Fallback based on current locale (translate to user's preferred language)
        if ($currentLocale === 'es') {
            return translate('listing_details.translate_to_spanish');
        } else {
            return translate('listing_details.translate_to_english');
        }
    }
}
