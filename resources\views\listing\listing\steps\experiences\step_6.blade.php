@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "detail-step");
@endphp
<fieldset class="basic_accommodation_step basic_watercraft_step accommodations_rules_step basics_experience_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="main_step_subtitle">
                            <p>{{ $step_data->sub_title }}</p>
                        </div>
                    @endisset
                    <div class="space_details_wrapper">
                        <div class="space_detail_single">
                            <div class="space_detail_title">
                                <label class="px-0" for="total_booking_capacity">{{ translate('stepper.total_booking_capacity') }}</label>
                            </div>
                            <div class="space_detail_quantity_wrapper">
                                <button class="btn minus_btn" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                    <i class="fa fa-minus" aria-hidden="true"></i>
                                </button>
                                <input type="number" min="1"
                                    value="{{ $listing->detail->booking_capacity ?? '1' }}"
                                    class="form-control file no_validate" name="booking_capacity"
                                    id="total_booking_capacity" required />
                                <button class="btn plus_btn" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                        <div class="space_detail_single allow_pets_field_wrapper mt-0">
                            <div class="space_detail_title">
                                {{-- <label class="px-0" for="minimum_booking_capacity">Minimum Booking Capacity</label> --}}
                                <label class="px-0 text-start" for="minimum_booking_capacity">{{ translate('stepper.offer_private_booking') }}</label>
                            </div>
                            <div class="allow_pets_input_wrapper">
                                <div class="allow_pets_input">
                                    <label for="private">{{ translate('stepper.yes') }}</label>
                                    <input type="radio" class="radio_btn toggle" id="private" value="yes"
                                        {{ ($listing->detail->private_booking ?? '') == 'yes' ? 'checked' : '' }}
                                        name="private_booking" />
                                </div>
                                <div class="allow_pets_input">
                                    <label for="not_private">{{ translate('stepper.no') }}</label>
                                    <input type="radio" class="radio_btn toggle" id="not_private" value="no"
                                        {{ ($listing->detail->private_booking ?? '') == 'no' || !isset($listing->detail->private_booking) ? 'checked' : '' }}
                                        name="private_booking" />
                                </div>
                            </div>
                        </div>
                        <div class="space_detail_single">
                            <div class="allow_pets_field_wrapper allow_children_field_wrapper mt-0">
                                <div class="allow_pets_title">
                                    <h4  class=" text-start">{{ translate('stepper.are_children_allow') }}</h4>
                                </div>
                                <div class="allow_pets_input_wrapper">
                                    <div class="allow_pets_input">
                                        <label for="allowed">{{ translate('stepper.yes') }}</label>
                                        <input type="radio" class="radio_btn toggle" id="allowed" value="yes"
                                            {{ ($listing->detail->child_allow ?? '') == 'yes' ? 'checked' : '' }}
                                            name="child_allow" />
                                    </div>
                                    <div class="allow_pets_input">
                                        <label for="not_allowed">{{ translate('stepper.no') }}</label>
                                        <input type="radio" class="radio_btn toggle" id="not_allowed" value="no"
                                            {{ ($listing->detail->child_allow ?? '') == 'no' || !isset($listing->detail->child_allow) ? 'checked' : '' }}
                                            name="child_allow" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="space_detail_single custom_child_allowed {{ ($listing->detail->child_allow ?? '') == 'no' ? ' d-none' : '' }}">
                            <div class="space_detail_title">
                                <label class="px-0" for="min-age">{{ translate('stepper.define_child_age_range') }}</label>
                            </div>
                            <div class="custom_children_wrapper d-flex gap-3 align-items-center">
                                <div class="add_btn_wrapper_child_age w-50">
                                    <input type="number" class="form-control no_validate text-center" id="child_age_from" placeholder="{{ translate('stepper.min_age') }}"
                                        value="{{ $listing->detail->child_age_from ?? '' }}" name="child_age_from" />
                                </div>
                                <span>{{ translate('stepper.to') }}</span>
                                <div class="child_age_wrapper w-50">
                                    <input type="number" class="form-control no_validate text-center" id="child_age_to" placeholder="{{ translate('stepper.max_age') }}"
                                        value="{{ $listing->detail->child_age_to ?? '' }}" name="child_age_to" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {
            $('.allow_children_field_wrapper .allow_pets_input_wrapper [name="child_allow"]').on('change',
            function() {
                var childAllow = $(this).val();
                var allowParent = $('.custom_child_allowed');

                if (childAllow == 'yes') {
                    setTimeout(() => {
                        allowParent.removeClass('d-none');
                        allowParent.find('input').removeClass('no_validate').val('');
                    }, 200);

                } else {
                    allowParent.addClass('d-none');
                    allowParent.find('input').addClass('no_validate').val('');
                    // $('.hidden_price_children, #price_children').val('1');
                    $('.hidden_price_children, #price_children').val('');
                    $('.hidden_price_children, #price_children').trigger('input');
                }
            });


            var allowedChildValue = $(
                '.allow_children_field_wrapper .allow_pets_input_wrapper [name="child_allow"]:checked').val();
            if (allowedChildValue == 'no') {
                $('.custom_child_allowed').addClass('d-none');
                $('.custom_child_allowed input').addClass('no_validate');
            } else {
                $('.custom_child_allowed').removeClass('d-none');
                $('.custom_child_allowed input').removeClass('no_validate');
            }

        });



        document.addEventListener("DOMContentLoaded", function() {
            const childAgeFrom = document.getElementById("child_age_from");
            const childAgeTo = document.getElementById("child_age_to");

            function validateOnBlur(inputField) {
                let value = parseInt(inputField.value, 10);

                // If empty, allow user to type (don't block deletion)
                if (isNaN(value)) return;

                // Restrict values between 1 and 18
                if (value < 1) {
                    inputField.value = 1;
                    Swal.fire(@json(translate('stepper.oops')), @json(translate('stepper.age_min_error')), "error");
                } else if (value > 18) {
                    inputField.value = 18;
                    Swal.fire(@json(translate('stepper.oops')), @json(translate('stepper.age_max_error')), "error");
                }

                validateAge();
            }

            function validateAge() {
                let fromValue = parseInt(childAgeFrom.value, 10);
                let toValue = parseInt(childAgeTo.value, 10);

                // Don't validate if user is still typing
                if (isNaN(fromValue) || isNaN(toValue)) return;

                // Only check range after full input
                if (toValue < fromValue) {
                    Swal.fire(@json(translate('stepper.oops')), @json(translate('stepper.age_range_error')), "error");
                    childAgeTo.value = fromValue; // Reset End Age to match Start Age
                }
            }

            // Validate after user finishes typing (on blur)
            childAgeFrom.addEventListener("blur", function() {
                validateOnBlur(childAgeFrom);
            });

            childAgeTo.addEventListener("blur", function() {
                validateOnBlur(childAgeTo);
            });

            // Allow typing freely without instant validation
            childAgeFrom.addEventListener("input", function() {
                // Only validate after full input, not while typing
                if (childAgeFrom.value === "" || childAgeTo.value === "") return;
            });

            childAgeTo.addEventListener("input", function() {
                if (childAgeFrom.value === "" || childAgeTo.value === "") return;
            });
        });
    </script>
@endpush
