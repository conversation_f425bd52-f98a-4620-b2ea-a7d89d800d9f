@push('css')
    <style>
        .loading {
            color: #666;
            font-style: italic;
        }

        .original-text {
            margin-bottom: 10px;
        }

        .translated-text {
            border-top: 1px solid #e0e0e0;
            padding-top: 10px;
            color: #555;
            font-style: italic;
        }
    </style>
@endpush
<div class="col-md-12">
    <div class="info px-4 pt-4 pb-4 d-flex flex-column justify-content-between">
        <h5 class="py-sm-3 py-1 d-flex justify-content-between gap-1 align-items-center flex-wrap">
            <span>{{ translate('listing_details.overall_rating') }}</span>
            <span class="rating">
                <i class="fas fa-star"></i>
                <span
                    class="fs-26 light-bold">{{ $listing->rating == 0 ? translate('listing_details.new') : number_format($listing->rating, 1) }}</span>
            </span>
        </h5>
        <div class="d-flex justify-content-between">
            <div class="d-flex">
                <div class="user_img me-md-3 me-2">
                    <img width="63" height="63" class="img-fluid rounded-circle"
                        src="{{ asset('website') . '/' . ($listing->latest_review->user->avatar ?? '') }}"
                        alt="user">
                </div>
                <div class="user_info">
                    <h6 class="">{{ $listing->latest_review->user->name ?? '-' }}
                    </h6>
                    <p>
                        {{-- {{ $listing->latest_review->created_at ?? '-' }} --}}
                        {{ $listing->latest_review->created_at->diffForHumans() }}
                    </p>
                </div>
            </div>
            <div class="user_rating">
                <p> <i class="fas fa-star btn-yellow"></i>
                    {{ $listing->latest_review->rating ?? '-' }}.0 </p>
            </div>
        </div>
        <div class="detail_info py-3">
            {!! $listing->latest_review->comment ?? '-' !!}
        </div>
        <div>
            <button type="button" class="button button1" data-bs-toggle="modal" data-bs-target="#preview_review">
                {{ translate('listing_details.view_all_reviews') }}
            </button>
        </div>
        <!-- Review Modal -->
        <div class="modal fade review" id="preview_review" tabindex="-1" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-all ">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header pb-0">
                            <span class="text-white">.</span>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                            </button>
                        </div>
                        <div class="modal-body mb-3">
                            <div class="row">
                                <div class="col-lg-4 col-md-5">
                                    <div class="review_sidebar v_divide">
                                        <h1 class="modal-title text-center">
                                            <i class="fa fa-star pe-2 btn-yellow"></i>
                                            {{ $listing->rating == 0 ? translate('listing_details.new') : number_format($listing->rating, 1) }}
                                        </h1>
                                        <div class="overall-rating py-3">
                                            <h6 class="overall semi-bold">{{ translate('listing_details.overall_rating') }}</h4>
                                                @php
                                                    // Initialize rating counts
                                                    $ratingCounts = [
                                                        1 => 0,
                                                        2 => 0,
                                                        3 => 0,
                                                        4 => 0,
                                                        5 => 0,
                                                    ];
                                                    // Count the ratings
                                                    $totalReviews = count($listing->reviews); // Total number of reviews
                                                    foreach ($listing->reviews as $review) {
                                                        $rating = (int) $review['rating']; // Ensure rating is an integer
                                                        if (isset($ratingCounts[$rating])) {
                                                            $ratingCounts[$rating]++;
                                                        }
                                                    }
                                                @endphp
                                                @for ($r = 5; $r >= 1; $r--)
                                                    <div class="single_rating rating_{{ $r }}">
                                                        <span class="number">{{ $r }}</span>
                                                        <div class="bar_rating_parent position-relative d-inline-block">
                                                            <span class="bar_rating d-inline-block"></span>
                                                            @php
                                                                $percentage =
                                                                    $totalReviews > 0
                                                                        ? ($ratingCounts[$r] / $totalReviews) * 100
                                                                        : 0;
                                                            @endphp
                                                            <span class="bar_rating filled  d-inline-block"
                                                                style="width: {{ round($percentage) }}%"></span>
                                                        </div>
                                                    </div>
                                                @endfor
                                        </div>
                                        <div class="parameter_review pt-1">
                                            <div class="single_option">
                                                <div
                                                    class="d-flex justify-content-between align-items-center  divider py-3">
                                                    <div
                                                        class="d-flex justify-content-between align-items-center gap-3">
                                                        <img src="{{ asset('website/images/clean.png') }}"
                                                            alt="" height="20px" width="20px">
                                                        <h6 class="heading m-0">{{ translate('listing_details.cleanliness') }}</h6>
                                                    </div>
                                                    <p class="rating m-0">
                                                        {{ number_format($listing->reviews->avg('cleanliness'), 1) }}
                                                    </p>
                                                </div>
                                                <div
                                                    class="d-flex justify-content-between align-items-center  divider py-3">
                                                    <div
                                                        class="d-flex justify-content-between align-items-center gap-3">
                                                        <img src="{{ asset('website/images/speech-bubble.png') }}"
                                                            alt="" height="20px" width="20px">
                                                        <h6 class="heading m-0">{{ translate('listing_details.communication') }}</h6>
                                                    </div>
                                                    <p class="rating m-0">
                                                        {{ number_format($listing->reviews->avg('communication'), 1) }}
                                                    </p>
                                                </div>
                                                <div
                                                    class="d-flex justify-content-between align-items-center  divider py-3">
                                                    <div
                                                        class="d-flex justify-content-between align-items-center gap-3">
                                                        <img src="{{ asset('website/images/amenity.png') }}"
                                                            alt="" height="20px" width="20px">
                                                        <h6 class="heading m-0">{{ translate('listing_details.amenities_features') }}</h6>
                                                    </div>
                                                    <p class="rating m-0">
                                                        {{ number_format($listing->reviews->avg('features'), 1) }}</p>
                                                </div>
                                                <div
                                                    class="d-flex justify-content-between align-items-center  divider py-3">
                                                    <div
                                                        class="d-flex justify-content-between align-items-center gap-3">
                                                        <img src="{{ asset('website/images/value_for_money.png') }}"
                                                            alt="" height="20px" width="20px">
                                                        <h6 class="heading m-0">{{ translate('listing_details.value_for_money') }}</h6>
                                                    </div>
                                                    <p class="rating m-0">
                                                        {{ number_format($listing->reviews->avg('value_of_money'), 1) }}
                                                    </p>
                                                </div>
                                                <div
                                                    class="d-flex justify-content-between align-items-center  divider py-3">
                                                    <div
                                                        class="d-flex justify-content-between align-items-center gap-3">
                                                        <img src="{{ asset('website/images/accuracy.png') }}"
                                                            alt="" height="20px" width="20px">
                                                        <h6 class="heading m-0">{{ translate('listing_details.accuracy_of_description') }}</h6>
                                                    </div>
                                                    <p class="rating m-0">
                                                        {{ number_format($listing->reviews->avg('accuracy_of_description'), 1) }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-8 col-md-7">
                                    <div class="review_body">
                                        <div class="review_header">
                                            <div class="d-flex justify-content-between align-items-center pb-4">
                                                <h4 class="modal-heading m-0">
                                                    @if (count($listing->reviews) == 1)
                                                        {{ count($listing->reviews) }}
                                                        {{ ucwords(translate('listing_details.review')) }}
                                                    @else
                                                        {{ count($listing->reviews) }}
                                                        {{ ucwords(translate('listing_details.reviews')) }}
                                                    @endif
                                                </h4>
                                                <div class="dropdown filter_dropdown">
                                                    <a class="dropdown-btn dropdown-toggle" href="#"
                                                        role="button"data-bs-toggle="dropdown" aria-expanded="true">
                                                        {{ translate('listing_details.filter') }}
                                                    </a>
                                                    <ul class="dropdown-menu" aria-labelledby="languageDropdown"
                                                        data-bs-popper="none">
                                                        <li>
                                                            <a class="dropdown-item" data-value="most-recent">{{ translate('listing_details.most_recent') }}</a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item"
                                                                data-value="highest-rating">{{ translate('listing_details.highest_rating') }}</a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item"data-value="lowet-rating">{{ translate('listing_details.lowest_rating') }}</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="search d-flex px-3 py-1">
                                                <input type="text" class="form-control py-0" id="searchInput"
                                                    placeholder="{{ translate('listing_details.search_reviews') }}">
                                                <button class="btn">
                                                    <i class="fa fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="review_wrapper" id="reviewWrapper">
                                            @foreach ($listing->reviews as $review)
                                                <div class="reviews pt-4 divider"
                                                    data-review-rating="{{ $review->rating }}"
                                                    data-review-created="{{ $review->created_at->format('Y-m-d H:i:s') }}"
                                                    data-review-text="{!! $review->comment !!}"
                                                    >
                                                    <div
                                                        class="review_head d-flex justify-content-between align-items-start">
                                                        <div class="d-flex align-items-center ">
                                                            <div class="user_img me-md-3 me-2">
                                                                <img width="43" height="43"
                                                                    class="img-fluid rounded-circle"
                                                                    src="{{ asset('website') . '/' . $review->user->avatar }}"
                                                                    alt="user" onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                                            </div>
                                                            <h6 class="fs-14 m-0">{{ $review->user->name }}</h6>
                                                        </div>
                                                        <div class="report-review parent_flag">
                                                            @auth
                                                                <button class="flag report-btn report_review_btn trans_btn"
                                                                    data-bs-toggle="modal" data-bs-target="#report_review"
                                                                    data-type="review"
                                                                    data-review-id="{{ $review->id }}">
                                                                    <i class="fas fa-flag "></i>
                                                                </button>
                                                            @else
                                                                <a class="flag report-btn report_review_btn trans_btn"
                                                                    data-bs-toggle="modal" data-bs-dismiss="modal"
                                                                    data-bs-target="#login">
                                                                    <i class="fas fa-flag "></i>
                                                                </a>
                                                            @endauth
                                                        </div>
                                                    </div>
                                                    <div class="review_body pt-1">
                                                        <div class="d-flex gap-2 align-items-center mb-2">
                                                            <p class="mb-1">
                                                                @for ($pr = 1; $pr <= $review->rating ?? '0'; $pr++)
                                                                    <i class="fa fa-star fs-10 rating_star"></i>
                                                                @endfor
                                                            </p>
                                                            <p class="fs-12 text-black-50 m-0">
                                                                {{ $review->created_at->diffForHumans() }}
                                                            </p>
                                                        </div>
                                                        @if(isset($review->comment) && !empty($review->comment))
                                                            <div class="comment-text" data-original-text="{{ strip_tags($review->comment) }}">
                                                                <div class="original-text fs-14">{!! $review->comment !!}</div>
                                                                <div class="translated-text d-none"></div>
                                                                
                                                                @if(shouldShowTranslateButton($review->comment))
                                                                    <button class="trans_btn translate-btn" data-lang="{{ detectLanguageModernMT($review->comment) === 'es' ? 'en' : 'es' }}">
                                                                        <img src="{{ asset('website') }}/images/translate.svg" class="translate_img" height="10px" width="10px">
                                                                        {{ getTranslateButtonText($review->comment) }}
                                                                    </button>
                                                                    <button class="trans_btn show-original-btn d-none" data-lang="{{ detectLanguageModernMT($review->comment) }}">
                                                                        <img src="{{ asset('website') }}/images/translate.svg" class="translate_img" height="10px" width="10px">
                                                                        {{ translate('listing_details.show_original') }}
                                                                    </button>
                                                                @endif
                                                            </div>
                                                        @endif
                                                        <div class="image_wrapper">
                                                            @forelse ($review->images ?? [] as $review_img)
                                                                <a href="{{ asset('website') . '/' . $review_img->image }}"
                                                                    data-fancybox="gallery" class="mb-3">
                                                                    <img src="{{ asset('website') . '/' . $review_img->image }}"
                                                                        alt="" class="img-fluid image_single"
                                                                        height="50" width="50" onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                                                </a>
                                                            @empty
                                                            @endforelse
                                                        </div>

                                                        <div class="reply_wrapper">
                                                            <div class="ps-4">
                                                                <div class="replied_parent">
                                                                    @isset($review->reply->reply)
                                                                        <div
                                                                            class="d-flex align-items-center justify-content-between w-100">
                                                                            <div class="d-flex align-items-start ">
                                                                                <div class="user_img me-md-3 me-2">
                                                                                    <img width="30" height="30"
                                                                                        class="img-fluid rounded-circle"
                                                                                        src="{{ asset('website') . '/' . $review->provider->avatar }}"
                                                                                        alt="user">
                                                                                </div>
                                                                                <h6 class="fs-12 m-0">
                                                                                  {{translate('listing_details.response_from')  }}
                                                                                    {{ $review->provider->name }}
                                                                                    {{ $review->reply->is_updated == 1 ? translate('listing_details.updated')   : '' }}
                                                                                    <br>
                                                                                    <p class="fs-12 text-black-50 m-0">
                                                                                        {{ $review->reply->updated_at->diffForHumans() }}
                                                                                    </p>
                                                                                </h6>
                                                                            </div>
                                                                            <div
                                                                                class="button_wrapper d-flex gap-2 alogn-items-center">
                                                                                <div
                                                                                    class="cancel-edit-review parent_flag d-none ">
                                                                                    <button
                                                                                        class="flag edit_review_btn trans_btn cancel_edit">
                                                                                        <i class="fas fa-times "></i>
                                                                                    </button>
                                                                                </div>

                                                                                @if ($review?->reply->is_updated == 0 && $review?->reply?->updated_at->diffInHours(now()) < 24)
                                                                                    <div class="edit-review parent_flag">
                                                                                        <button
                                                                                            class="flag edit_review_btn trans_btn"
                                                                                            data-review-id="{{ $review->id }}_1">
                                                                                            <i class="fas fa-pen "></i>
                                                                                        </button>
                                                                                    </div>
                                                                                @endif

                                                                                <div class="parent_flag">
                                                                                    @auth
                                                                                        <button
                                                                                            class="flag report-btn response_report_review_btn trans_btn"
                                                                                            data-bs-toggle="modal"
                                                                                            data-bs-target="#response_report_review"
                                                                                            data-review-id="{{ $review->id }}">
                                                                                            <i class="fas fa-flag "></i>
                                                                                        </button>
                                                                                    @else
                                                                                        <a class="flag report-btn response_report_review_btn trans_btn"
                                                                                            href="{{ route('login') }}">
                                                                                            <i class="fas fa-flag "></i>
                                                                                        </a>
                                                                                    @endauth
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="comment-text" data-original-text="{{ strip_tags($review->reply->reply) }}">
                                                                            <div class="original-text reply_comment_text">{!! $review->reply->reply !!}</div>
                                                                            <div class="translated-text d-none"></div>
                                                                            
                                                                            @if(shouldShowTranslateButton($review->reply->reply))
                                                                                <button class="translate-btn trans_btn" data-lang="{{ detectLanguageModernMT($review->reply->reply) === 'es' ? 'en' : 'es' }}">
                                                                                    <img src="{{ asset('website') }}/images/translate.svg" class="translate_img" height="10px" width="10px">
                                                                                    {{ getTranslateButtonText($review->reply->reply) }}
                                                                                </button>
                                                                                <button class="show-original-btn d-none trans_btn" data-lang="{{ detectLanguageModernMT($review->reply->reply) }}">
                                                                                    <img src="{{ asset('website') }}/images/translate.svg" class="translate_img" height="15px" width="15px">
                                                                                    {{ translate('listing_details.show_original') }}
                                                                                </button>
                                                                            @endif
                                                                        </div>
                                                                    @endisset
                                                                </div>
                                                                @if (auth()->id() == $listing->user_id)
                                                                    @if (
                                                                        $review?->reply?->is_updated == 0 &&
                                                                            $review?->reply?->updated_at->diffInHours(now()) < 24 &&
                                                                            empty($review?->reply()->onlyTrashed()->first()) && !isset($review->reply->reply))
                                                                        <div class="reply_parent">
                                                                            <form
                                                                                action="{{ route('review.reply', $review->id) }}"
                                                                                method="POST"
                                                                                class="{{ ($review->reply->is_updated ?? null) == 0 ? '' : 'd-none' }}">
                                                                                @csrf
                                                                                <div
                                                                                    class="d-flex align-items-center justify-content-between gap-2 mb-3">
                                                                                    <div class="user_img">
                                                                                        <img width="43"
                                                                                            height="43"
                                                                                            class="img-fluid rounded-circle"
                                                                                            src="{{ asset('website') . '/' . $review->provider->avatar }}"
                                                                                            alt="user">
                                                                                    </div>
                                                                                    <input type="text"
                                                                                        id="review_input_{{ $review->id }}"
                                                                                        class="reply_comment"
                                                                                        name="reply"
                                                                                        placeholder="{{ translate('listing_details.write_a_reply') }}"
                                                                                        required>
                                                                                    <button
                                                                                        data-sp-name="{{ $review->provider->name }}"
                                                                                        class="button button1 review-reply-btn">
                                                                                        <i
                                                                                            class="fas fa-paper-plane"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </form>
                                                                        </div>
                                                                    @endif
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Review Modal End -->
        <!-- Reprt Review Modal -->
        <div class="modal fade review-reoprt" id="report_review" tabindex="-1" aria-labelledby="exampleModalLabel">
            <div class="modal-all ">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" id="report_review_form">
                        <div class="modal-header justify-content-center border-0">
                            <h4 class="modal-title text-center">{{ translate('listing_details.report_review') }}</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                            </button>
                        </div>
                        <form action="{{ route('report_review') }}" method="POST" class="report_review_form">
                            @csrf
                            <input type="hidden" name="review_id" id="review_id">
                            <input type="hidden" name="report_type" value="review">
                            <input type="hidden" value="{{ $listing->ids }}" name="listing_id">
                            <div class="modal-body">
                                <div class="reasons_wrapper">
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_1"
                                            value="{{ translate("listing_details.inappropriate_language") }}" checked>
                                        <label for="reason_1">{{ translate('listing_details.inappropriate_language') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_2"
                                            value="{{ translate("listing_details.discrimination_or_harassment") }}">
                                        <label for="reason_2">{{ translate('listing_details.discrimination_or_harassment') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_3"
                                            value="{{ translate("listing_details.private_information_shared") }} Shared">
                                        <label for="reason_3">{{ translate('listing_details.private_information_shared') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_4"
                                            value="{{ translate("listing_details.irrelevant_or_off_topic") }}">
                                        <label for="reason_4">{{ translate('listing_details.irrelevant_or_off_topic') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_5"
                                            value="{{ translate("listing_details.retaliation_or_personal_attack") }}">
                                        <label for="reason_5">{{ translate('listing_details.retaliation_or_personal_attack') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_6"
                                            value="{{ translate("listing_details.inaccurate_or_misleading_information") }}">
                                        <label for="reason_6">{{ translate('listing_details.inaccurate_or_misleading_information') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reason_7" value="{{ translate("listing_details.other") }}">
                                        <label for="reason_7">{{ translate('listing_details.other') }}</label>
                                    </div>
                                </div>

                                <div class="mb-3 mt-2">
                                    <textarea name="other_reason" id="other_reason" class="form-control other_reason d-none" rows="5"
                                        placeholder="{{ translate('listing_details.please_provide_more_details') }}"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer border-0">
                                <button type="submit"
                                    class="bg_dark_yellow btn button1 text-black w-100">{{ translate('listing_details.submit') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Reprt Review Modal End -->
        <!-- Report Response Modal -->
        <div class="modal fade review-reoprt" id="response_report_review" tabindex="-1"
            aria-labelledby="exampleModalLabel">
            <div class="modal-all ">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" id="response_report_review_form">
                        <div class="modal-header justify-content-center border-0">
                            <h4 class="modal-title text-center">{{ translate('listing_details.report_response') }}</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                            </button>
                        </div>
                        <form action="{{ route('report_review') }}" method="POST"
                            class="response_report_review_form">
                            @csrf
                            <input type="hidden" name="review_id" id="response_review_id">
                            <input type="hidden" name="report_type" value="response">
                            <input type="hidden" value="{{ $listing->ids }}" name="listing_id">
                            <div class="modal-body">
                                <div class="reasons_wrapper">
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_1"
                                            value="{{ translate("listing_details.inappropriate_language") }}" checked>
                                        <label for="reponse_reason_1">{{ translate('listing_details.inappropriate_language') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_2"
                                            value="{{ translate("listing_details.discrimination") }}">
                                        <label for="reponse_reason_2">{{ translate('listing_details.discrimination') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_3"
                                            value="{{ translate("listing_details.harassment_or_bullying") }}">
                                        <label for="reponse_reason_3">{{ translate('listing_details.harassment_or_bullying') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_4"
                                            value="{{ translate("listing_details.violates_my_privacy") }}">
                                        <label for="reponse_reason_4">{{ translate('listing_details.violates_my_privacy') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_5"
                                            value="{{ translate("listing_details.spam_or_irrelevant") }}">
                                        <label for="reponse_reason_5">{{ translate('listing_details.spam_or_irrelevant') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_6"
                                            value="{{ translate("listing_details.false_or_misleading_information") }}">
                                        <label for="reponse_reason_6">{{ translate('listing_details.false_or_misleading_information') }}</label>
                                    </div>
                                    <div class="custom_radio">
                                        <input type="radio" name="subject" id="reponse_reason_7" value="{{ translate("listing_details.other") }}">
                                        <label for="reponse_reason_7">{{ translate('listing_details.other') }}</label>
                                    </div>
                                </div>
                                <div class="mb-3 mt-2">
                                    <textarea name="other_reason" id="other_reason" class="form-control other_reason d-none" rows="5"
                                        placeholder="{{ translate('listing_details.please_provide_more_details') }}"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer border-0">
                                <button type="submit"
                                    class="bg_dark_yellow btn button1 text-black w-100">{{ translate('listing_details.submit') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Report Response Modal End -->
    </div>
</div>
@push('js')
    <script>
        $(document).on('change', '#report_review [name="subject"]', function() {
            let input = $("#report_review input[name='subject']:checked").val();
            if (input == 'Other') {
                $('.other_reason').removeClass('d-none');
            } else {
                $('.other_reason').addClass('d-none');
            }
        });
        $(document).ready(function() {
            $('#searchInput').on('input', function() {
                const searchTerm = $(this).val().trim().toLowerCase();
                $('#reviewWrapper .reviews').each(function() {
                    const reviewText = $(this).attr('data-review-text')
                        .toLowerCase();
                    if (reviewText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
            $(document).on('click', '.report_review_btn', function() {
                const reviewId = $(this).attr('data-review-id');
                $('#review_id').val(reviewId);
            });

            $(document).on('click', '.response_report_review_btn', function() {
                const reviewId = $(this).attr('data-review-id');
                $('#response_review_id').val(reviewId);
            });
        });
    </script>
    <!-- Add this script after your existing search script -->
    <script>
        $(document).ready(function() {
            // Handle filter dropdown selection
            $('.filter_dropdown .dropdown-item').on('click', function(e) {
                e.preventDefault();
                const filterValue = $(this).data('value');
                sortReviews(filterValue);

                // Update dropdown text
                $('.dropdown-btn').text($(this).text());
            });

            // Function to sort reviews
            function sortReviews(filterValue) {
                const $wrapper = $('#reviewWrapper');
                const $reviews = $wrapper.find('.reviews').get();

                $reviews.sort(function(a, b) {
                    const $a = $(a);
                    const $b = $(b);

                    if (filterValue === 'most-recent') {
                        // Sort by most recent (newest first)
                        const dateA = new Date($a.data('review-created'));
                        const dateB = new Date($b.data('review-created'));
                        return dateB - dateA;
                    } else if (filterValue === 'highest-rating') {
                        // Sort by highest rating
                        const ratingA = parseInt($a.data('review-rating'));
                        const ratingB = parseInt($b.data('review-rating'));
                        return ratingB - ratingA;
                    } else if (filterValue === 'lowet-rating') {
                        // Sort by lowest rating
                        const ratingA = parseInt($a.data('review-rating'));
                        const ratingB = parseInt($b.data('review-rating'));
                        return ratingA - ratingB;
                    }

                    return 0;
                });

                // Detach and reattach sorted reviews
                $wrapper.empty().append($reviews);
            }

            // Initialize with most recent
            sortReviews('most-recent');

            // Search functionality
            $('#searchInput').on('input', function() {
                const searchTerm = $(this).val().trim().toLowerCase();
                $('#reviewWrapper .reviews').each(function() {
                    const reviewText = $(this).data('review-text').toLowerCase();
                    if (reviewText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            @if (session()->has('review_reply_success'))
                $('#preview_review').modal('show');
            @endif
            // Review btn 
            // $(document).on("click", ".review-reply-btn", function(){
            //     let review_reply_id = $(this).data("reply_id");
            //     let review_reply = $(`#review_input_${review_reply_id}`).val();
            //     $.ajax({
            //         url: `route("review.reply", "")/${review_reply_id}`,
            //         type: "POST",
            //         data: {
            //             reply: review_reply,
            //             _token: `{{ csrf_token() }}`
            //         },
            //         success: function(response){
            //             console.log(response)
            //         }
            //     })
            // })
            // Review btn end 

        });


        // Review reply and its edit (Runtime)
        $(document).on('click', '.review-reply-btn', function(e) {
            e.preventDefault();

            var commentReply = $(this).closest('.reviews').find('.reply_comment').val();
            var image = $(this).closest('.reviews').find('.reply_wrapper .user_img img').attr('src');
            var replyId = $(this).closest('.reviews').find('.report_review_btn').attr('data-review-id');
            var userName = $(this).attr('data-sp-name');
            var form = $(this).closest('form');
            var main_review_cont = $(this);
            main_review_cont.attr('disabled', true); // Disable the button to prevent multiple clicks
            var formData = form.serialize(); // Serialize form data

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.status == true) {
                        main_review_cont.closest('.reviews').find(
                            '.review_body .reply_wrapper .replied_parent').html(`
                            <div class="ps-4 replied">
                                <div class="d-flex align-items-center justify-content-between w-100">
                                    <div class="d-flex align-items-start ">
                                        <div class="user_img me-md-3 me-2">
                                            <img width="30" height="30" class="img-fluid rounded-circle" src="${image}" alt="user">
                                        </div> 
                                        <h6 class="fs-12 m-0 "><span class="user_name">Response from ${userName} </span><span class="reply_status">${ response.data.is_updated == 0 ? '' : '(Updated)'}</span> <br> <p class="fs-12 text-black-50 m-0">Just now </p> </h6>
                                    </div>
                                    <div class="button_wrapper d-flex gap-2 alogn-items-center"> 
                                        <div class="cancel-edit-review parent_flag d-none ">
                                            <button class="flag edit_review_btn trans_btn cancel_edit" ><i class="fas fa-times "></i>
                                            </button> 
                                        </div>
                                        <div class="edit-review parent_flag" data-update="${ response.data.is_updated == 0 ? '0' : '1'}">
                                            <button class="flag edit_review_btn trans_btn" data-review-id="${replyId}_1"><i class="fas fa-pen "></i>
                                            </button> 
                                        </div>
                                        <div class="report-review parent_flag">
                                            <button class="flag report-btn report_review_btn trans_btn" data-bs-toggle="modal"  data-bs-target="#report_review" data-review-id="${replyId}_1"><i class="fas fa-flag "></i>
                                            </button> 
                                        </div>
                                    </div>
                                </div> 
                                <p class="reply_comment_text"> ${commentReply}</p>
                            </div>
                        `);
                        // Hide the form after reply
                        main_review_cont.closest('.reply_parent').hide();
                    } else {
                        Swal.fire({
                            title: "Error",
                            text: response.message,
                            icon: "error"
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: @json(translate('listing_details.error')),
                        text: @json(translate('listing_details.error_submitting_reply')),
                        icon: "error"
                    });
                },
                complete: function() {
                    main_review_cont.attr('disabled', false); // Re-enable the button
                }
            });
        });
        // $(document).on('click', '.review-reply-btn', function(e) {
        //     e.preventDefault();
        //     var commentReply = $(this).closest('.reviews').find('.reply_comment').val();
        //     var image = $(this).closest('.reviews').find('.reply_wrapper .user_img img').attr('src');
        //     var replyId = $(this).closest('.reviews').find('.report_review_btn').attr('data-review-id');

        //     $.ajax({
        //         url: `{{ route('review.reply', '') }}/`
        //     })
        //     $(this).closest('.reviews').find('.review_body .reply_wrapper').prepend(`
    //         <div class="ps-4 replied_parent">
    //             <div class="d-flex align-items-center justify-content-between w-100">
    //                 <div class="d-flex align-items-start ">
    //                     <div class="user_img me-md-3 me-2">
    //                         <img width="30" height="30" class="img-fluid rounded-circle" src="${image}" alt="user">
    //                     </div> 
    //                     <h6 class="fs-12 m-0 "><span class="user_name">Response from Testing </span><span class="reply_status"></span> <br> <p class="fs-12 text-black-50 m-0">Just now </p> </h6>
    //                 </div>
    //                 <div class="button_wrapper d-flex gap-2 alogn-items-center"> 
    //                     <div class="cancel-edit-review parent_flag d-none ">
    //                         <button class="flag edit_review_btn trans_btn cancel_edit" ><i class="fas fa-times "></i>
    //                         </button> 
    //                     </div>
    //                     <div class="edit-review parent_flag">
    //                         <button class="flag edit_review_btn trans_btn" data-review-id="${replyId}_1"><i class="fas fa-pen "></i>
    //                         </button> 
    //                     </div>
    //                     <div class="report-review parent_flag">
    //                         <button class="flag report-btn report_review_btn trans_btn" data-bs-toggle="modal" data-bs-dismiss="modal" data-bs-target="#report_review" data-review-id="${replyId}_1"><i class="fas fa-flag "></i>
    //                         </button> 
    //                     </div>
    //                 </div>
    //             </div> 
    //             <p class="reply_comment_text"> ${commentReply}</p>
    //         </div>
    //     `);

        //     $(this).closest('.reply_parent').hide();
        // });
        $(document).on('click', '.edit_review_btn', function(e) {

            $(this).closest('.reply_wrapper').find('.reply_parent').show();
            var reply = $(this).closest('.reply_wrapper').find('.reply_comment_text').text().trim();
            $(this).closest('.reply_wrapper').find('.reply_comment').val(reply);
            $(this).closest('.reply_wrapper').find('.reply_parent .review-reply-btn').addClass('edit-reply-btn');
            // $(this).closest('.reply_wrapper').find('.reply_parent .review-reply-btn').removeClass(
            //     'review-reply-btn');
            $(this).closest('.reply_wrapper').find('.replied_parent .cancel-edit-review').removeClass('d-none')
                .show();


            $(this).parent().hide();
            $(this).closest('.reply_wrapper').find('.reply_parent .edit-reply-btn').prop('disabled', true);

        });
        $(document).on('click', '.cancel_edit', function(e) {
            var oldReply = $(this).closest('.reviews').find('.reply_comment_text').text().trim();

            $(this).closest('.reply_wrapper').find('.reply_parent').hide();
            $(this).parent().hide();
            $(this).closest('.reviews').find('.reply_comment').val(oldReply);
            $(this).closest('.reply_wrapper').find('.edit-review').show();

        });
        $(document).on('click', '.edit-reply-btn', function(e) {
            e.preventDefault();

            var editReply = $(this).closest('.reviews').find('.reply_comment').val();
            $(this).closest('.reply_wrapper').find('.replied_parent .reply_comment_text').text(editReply);
            $(this).closest('.reply_wrapper').find('.replied_parent .reply_status').text('(Updated)');
            $(this).closest('.reply_parent').hide();

            $(this).closest('.reply_wrapper').find(
                '.replied_parent .cancel-edit-review, .replied_parent .edit-review').hide();

        });
        $(document).on('keyup', '.reply_comment', function(e) {
            // To check if value is same or not
            var oldReply = $(this).closest('.reviews').find('.reply_comment_text').text().trim();
            var oldReplyLower = oldReply.toLowerCase();
            var newReply = $(this).val().toLowerCase();

            if (oldReplyLower == newReply) {
                $(this).closest('.reply_parent').find('.edit-reply-btn').prop('disabled', true);
            } else {
                $(this).closest('.reply_parent').find('.edit-reply-btn').prop('disabled', false);
            }
        });

        $(document).ready(function() {
            // Improved language detection using character and word analysis
            function detectLanguage(text) {
                if (!text || text.trim() === '') return 'en'; // Fallback to English for empty text

                // Remove URLs, numbers, and special characters
                const cleanText = text.replace(/https?:\/\/\S+|[\d\W]/g, ' ').toLowerCase().trim();

                // Common Spanish and English indicators
                const spanishIndicators = {
                    chars: ['á', 'é', 'í', 'ó', 'ú', 'ñ', 'ü'],
                    words: ['el', 'la', 'de', 'que', 'y', 'en', 'por', 'con', 'es', 'un', 'una'],
                    endings: ['ción', 'sión', 'mente', 'ando', 'iendo', 'amos', 'emos']
                };
                const englishIndicators = {
                    chars: ['w', 'k', 'y'], // Less common in Spanish
                    words: ['the', 'and', 'to', 'of', 'a', 'in', 'for', 'with', 'is', 'on'],
                    endings: ['ing', 'tion', 'ment', 'ness', 'able']
                };

                let score = {
                    es: 0,
                    en: 0
                };

                // Check for special characters
                spanishIndicators.chars.forEach(char => {
                    if (cleanText.includes(char)) score.es += 5;
                });
                englishIndicators.chars.forEach(char => {
                    if (cleanText.includes(char)) score.en += 3;
                });

                // Check for common words and endings
                const words = cleanText.split(/\s+/).filter(w => w.length > 1);
                words.forEach(word => {
                    if (spanishIndicators.words.includes(word)) score.es += 3;
                    if (englishIndicators.words.includes(word)) score.en += 3;

                    spanishIndicators.endings.forEach(ending => {
                        if (word.endsWith(ending)) score.es += 4;
                    });
                    englishIndicators.endings.forEach(ending => {
                        if (word.endsWith(ending)) score.en += 4;
                    });
                });

                // If scores are close or zero, default to English
                if (Math.abs(score.es - score.en) < 3 && score.es < 5 && score.en < 5) {
                    return 'en';
                }

                return score.es > score.en ? 'es' : 'en';
            }

            function translateText(text, targetLang, callback) {
                $('.comment-text').css('opacity', '0.7');

                $.ajax({
                    url: '{{ route('translate.dynamic') }}',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        text: text,
                        target_language: targetLang,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Accept': 'application/json',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    success: function(data) {
                        $('.comment-text').css('opacity', '1');
                        const translatedText = data.translated_text || text;
                        callback(translatedText);
                    },
                    error: function(xhr, status, error) {
                        $('.comment-text').css('opacity', '1');
                        console.error('Translation error:', error);
                        console.error('Response:', xhr.responseText);
                        callback(text);
                    }
                });
            }


            // Function to set button attributes and text based on language
            function setButtonAttributes($comment, detectedLang) {
                const targetLang = detectedLang === 'en' ? 'es' : 'en';
                const buttonText = detectedLang === 'en' ? 'Translate to Spanish' : 'Translate to English';

                $comment.find('.translate-btn')
                    .data('lang', targetLang)
                    .text(buttonText);
                $comment.find('.show-original-btn')
                    .data('lang', detectedLang);
            }

            // Handle translate and show original button clicks
            $(document).on('click', '.translate-btn, .show-original-btn', function(e) {
                e.preventDefault();
                const $button = $(this);
                const $commentElement = $button.closest('.comment-text');
                const originalText = $commentElement.data('original-text');
                const targetLang = $button.data('lang');

                if ($button.hasClass('show-original-btn')) {
                    // Show original text
                    $commentElement.find('.translated-text').addClass('d-none').empty();
                    $commentElement.find('.original-text').removeClass('d-none');
                    $commentElement.find('.translate-btn').removeClass('d-none');
                    $commentElement.find('.show-original-btn').addClass('d-none');
                } else {
                    // Show loading and translate
                    $commentElement.find('.original-text').addClass('d-none');
                    $commentElement.find('.translated-text').removeClass('d-none').html(
                        '<span class="loading">Translating...</span>');

                    translateText(originalText, targetLang, function(translatedText) {
                        $commentElement.find('.translated-text').html(translatedText);
                        $commentElement.find('.translate-btn').addClass('d-none');
                        $commentElement.find('.show-original-btn').removeClass('d-none');
                    });
                }
            });

            // Initialize buttons based on content language
            $('.comment-text').each(function() {
                const $comment = $(this);
                const text = $comment.data('original-text');
                const detectedLang = detectLanguage(text);
                setButtonAttributes($comment, detectedLang);
            });
        });
    </script>
@endpush
