@push('css')
    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <style>
        .image_uploader .card-title {
            font-size: 26px;
            font-weight: 600;
        }

        .image-preview {
            position: relative;
            display: inline-block;
        }

        .image-preview img {
            object-fit: cover;
            border-radius: 5px;
            height: 250px;
            width: 100%;
            margin-bottom: 20px;
        }

        .image-preview .error-message {
            position: absolute;
            top: 10px;
            left: 25px;
            background: #e1505ec9;
            /* height: 28px; */
            font-size: 12px !important;
            color: #fff !important;
            padding: 0px 5px;
            border-radius: 5px;
        }

        .remove-image {
            position: absolute;
            top: 10px;
            right: 25px;
            color: #E1505E !important;
            background: #fff;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            text-align: center;
            padding-top: 1px;
            cursor: pointer;
            font-size: 13px;
        }
    </style>
@endpush
@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', 'image-upload');
@endphp

<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
            <h2>{{ $step_data->title ?? '' }}</h2>
        </div>
        <div class="step_description">
            <p>{{ $step_data->sub_title ?? '' }}</p>
        </div>
        {{-- upload image modal --}}

        <!-- Button trigger modal -->
        {{-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#image-uploader-modal">
            Upload Image
        </button> --}}

        <!-- Modal -->
        <div class="modal fade image_uploader" id="image-uploader-modal" tabindex="-1" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header justify-content-between align-items-center border-0 pb-0">
                        <button type="button" class="btn-close m-0" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                        <div>
                            <h4 class="modal-title ">{{ translate('stepper.upload_photos') }}</h4>
                            <p class="fs-12"><strong>{{ translate('stepper.valid_images') }}:
                                    <span id="validImageCount">0</span></strong>
                            </p>
                        </div>
                        <label for="imageUpload" class="btn-add">+</label>
                    </div>
                    <div class="modal-body p-0">
                        <div class="container">
                            <div class="card border-dashed border-0">
                                <div class="card-body p-0">
                                    <!-- File Input -->
                                    <label for="imageUpload" class="d-block">
                                        <div class="image-upload_wrapper">
                                            <input type="file" class="d-none no_validate" id="imageUpload" multiple
                                                accept=".jpg, .jpeg, .png, .heif, .heic">
                                            <img src="{{ asset('website/images/Text.png') }}"
                                                alt="{{ translate('stepper.upload_photos') }}" height="40px"
                                                width="40px">
                                            <h5 class="card-title">{{ translate('stepper.drag_and_drop') }} </h5>
                                            <h6 class="text-muted fs-12">{{ translate('stepper.or_browse_for_photos') }}
                                            </h6>
                                            <label class="button text-center px-3 black_btn"
                                                for="imageUpload">{{ translate('stepper.browse') }}</label>
                                        </div>
                                    </label>
                                    <!-- Image Preview Container -->
                                    <div id="imagePreview" class="row mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-between border-0">
                        <button type="button" class="button black_btn"
                            data-bs-dismiss="modal">{{ translate('stepper.cancel') }}</button>
                        <button type="button" id="uploadImages"
                            class="button btn_yellow">{{ translate('stepper.upload') }}</button>
                    </div>
                </div>
            </div>
        </div>

        {{-- upload image modal end --}}

        <div class="drag_drop_photos_wrapper scrollable-section">
            @php
                $coverImage = $listing?->gallery_images->firstWhere('is_cover', 1) ?? null;
                $otherImages =
                    $listing?->gallery_images->reject(fn($image) => $image->is_cover == 1)->sortBy('sort_order') ?? [];
            @endphp

            @if (isset($coverImage))
                <div class="drag_drop_photo_single">
                    <img alt="Preview Cover" src="{{ asset('website') . '/' . $coverImage->url }}" loading="lazy">
                    <div class="dropdown">
                        <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item delete_btn" data-image-id="{{ $coverImage->id }}"
                                    href="#">{{ translate('stepper.delete') }}</a>
                            </li>
                        </ul>
                    </div>
                </div>
            @endif

            @forelse ($otherImages ?? [] as $gallery_image)
                <div class="drag_drop_photo_single sortable-element" data-image-id="{{ $gallery_image->id }}">
                    <img alt="Preview {{ $loop->index }}" src="{{ asset('website') . '/' . $gallery_image->url }}"
                        loading="lazy">
                    <div class="dropdown">
                        <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item make_cover_btn" data-image-id="{{ $gallery_image->id }}"
                                    href="#">{{ translate('stepper.make_cover_photo') }}
                                    {{ $gallery_image->sorting }}</a>
                            </li>
                            <li>
                                <a class="dropdown-item delete_btn" data-image-id="{{ $gallery_image->id }}"
                                    href="#">{{ translate('stepper.delete') }}</a>
                            </li>
                        </ul>
                    </div>
                </div>
            @empty
            @endforelse

            <div class="drag_drop_photo_single add_photo_box">
                <div class="photo_icon">
                    <img src="{{ asset('website') }}/images/digital_camera.png" alt="">
                </div>
                <div class="add_photo_btn">
                    <label class="add_photos_lbl photo_upload_btn">{{ translate('stepper.add_photos') }}</label>
                    <label class="plus_icon_lbl photo_upload_btn"><i class="fa fa-plus" aria-hidden="true"></i></label>
                    <input id="add_photo_file" class="no_validate" type="file" name="photos[]" multiple
                        accept="image/*">
                </div>
            </div>
        </div>
    </div>
</div>

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script type="module">
        $(document).ready(function() {
            const sortableContainer = $('.drag_drop_photos_wrapper');
            new Sortable(sortableContainer[0], {
                animation: 150,
                draggable: '.sortable-element',
                onEnd: function() {
                    const image_ids = sortableContainer
                        .children('.sortable-element')
                        .map(function() {
                            return $(this).data('image-id');
                        })
                        .get();

                    $.ajax({
                        url: "{{ route('listing_photo_order') }}",
                        type: "POST",
                        headers: {
                            "X-CSRF-TOKEN": "{{ csrf_token() }}",
                        },
                        data: {
                            image_ids: image_ids,
                            listing_id: $("input[name='listing_id']").val(),
                        },
                        success: function(response) {
                            if (response.status == false) {
                                Swal.fire({
                                    title: @json(translate('stepper.error')),
                                    text: response.message,
                                    icon: "error"
                                });
                                return;
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire({
                                title: @json(translate('stepper.error')),
                                text: @json(translate('stepper.failed_to_update_sort_order')),
                                icon: "error"
                            });
                            console.error(xhr.responseText);
                        },
                    });
                },
            });
        });

        // script closig and opening tags
    
        $(document).ready(function() {
            var imageNumber = $(
                '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single').length;
            if (imageNumber >= 6) {
                $('.listing_stepper .add_photos_step:not(.add_documents_step) .next.action-button').prop('disabled',
                    false);
            } else {
                $('.listing_stepper .add_photos_step:not(.add_documents_step) .next.action-button').prop('disabled',
                    true);
            }
        });

        function addBtn() {
            var imageLength = $('#imagePreview .image-preview').length;
            if (imageLength < 1) {
                console.log('imageNumber: ' + imageLength);
                $('.btn-add').css("opacity", '0');
            } else {
                $('.btn-add').css("opacity", '1');
            }
        }
        $('#imageUpload').on('change', function() {
            addBtn();
        });

        const container = $('.add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper');

        function imageValidation() {
            var imageNumber = $(
                '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single').length;
            if (imageNumber >= 6) {
                $('.listing_stepper .add_photos_step:not(.add_documents_step)').find('.next.action-button').prop('disabled',
                    false);
            } else {
                $('.listing_stepper .add_photos_step:not(.add_documents_step)').find('.next.action-button').prop('disabled',
                    true);
            }
        }
        // Next button validation function on image upload END

        function loadHeic2AnyLibrary() {
                return new Promise((resolve, reject) => {
                    if (window.heic2any) return resolve();

                    const script = document.createElement('script');
                    script.src = "{{ asset('website') }}/assets_cdn/js/heic2any.min.js";
                    script.onload = resolve;
                    script.onerror = () => reject(new Error('Failed to load HEIC conversion library'));
                    document.head.appendChild(script);
                });
            }

        async function handleFiles(files) {
            // Load library only if HEIC/HEIF files are found
            const hasHeic = Array.from(files).some(f =>
                f.name.toLowerCase().endsWith('.heic') ||
                f.name.toLowerCase().endsWith('.heif') ||
                f.type === 'image/heic' ||
                f.type === 'image/heif' ||
                f.type === ''
            );

            if (hasHeic && !window.heic2any) {
                await loadHeic2AnyLibrary();
            }

            // Convert HEIC/HEIF files to JPEG before upload
            const convertedFiles = await Promise.all(
                Array.from(files).map(async file => {
                    if (
                        file.name.toLowerCase().endsWith('.heic') ||
                        file.name.toLowerCase().endsWith('.heif') ||
                        file.type === '' // no type set
                    ) {
                        try {
                            const blob = await heic2any({
                                blob: file,
                                toType: "image/jpeg",
                                quality: 0.9
                            });
                            return new File([blob], file.name.replace(/\.[^/.]+$/, ".jpg"), { type: "image/jpeg" });
                        } catch (err) {
                            console.error("HEIC conversion failed", err);
                            return file; // fallback
                        }
                    }
                    return file;
                })
            );

            return convertedFiles;
        }

        // Image upload
        $('#add_photo_file').on('change', async function(event) {
            let listing_id = $('input[name="listing_id"]').val();
            let files = event.target.files;
            if (files.length === 0) {
                alert(@json(translate('stepper.no_files_selected')));
                return;
            }

            // Process and possibly convert HEIC/HEIF files
            files = await handleFiles(files);

            const listing_images = new FormData();
            for (let i = 0; i < files.length; i++) {
                listing_images.append("media[]", files[i]);
            }
            listing_images.append("listing_id", listing_id);
            listing_images.append("type", "image");


            let uploadedCount = 0;
            const totalFiles = files.length;

            Swal.fire({
                title: @json(translate('stepper.uploading_images')),
                html: `{{ translate('stepper.uploading') }} <b>0</b> {{ translate('stepper.out_of') }} ${totalFiles}`,
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });


            $.ajax({
                url: "{{ route('listingMedia') }}",
                type: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                },
                data: listing_images,
                contentType: false,
                processData: false,
                xhr: function() {
                    let xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable && Swal.isVisible()) {
                            let percentComplete = Math.round((evt.loaded / evt.total) * 100);
                            uploadedCount = Math.round((percentComplete / 100) *
                                totalFiles); // Estimate uploaded files
                            uploadedCount = Math.min(uploadedCount,
                                totalFiles); // Ensure it doesn't exceed totalFiles

                            Swal.update({
                                html: `{{ translate('stepper.uploading') }} <b>${uploadedCount}</b> {{ translate('stepper.out_of') }} ${totalFiles}...`,
                            });
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {

                    imageValidation();

                    if (response.status == true) {

                        // swal.close()

                        // Swal.fire({
                        //     title: "Uploading Images",
                        //     html: `Uploading <b>0</b> {{ translate('stepper.out_of') }} ${totalFiles}...`,
                        //     allowOutsideClick: false,
                        //     didOpen: () => {
                        //         Swal.showLoading();
                        //     },
                        // });

                        $.each(response.data, function(index, file) {
                            uploadedCount++;
                            Swal.update({
                                html: `{{ translate('stepper.uploading') }} <b>${uploadedCount}</b> {{ translate('stepper.out_of') }} ${totalFiles}...`,
                            });
                            const newDiv = $(
                                `<div class="drag_drop_photo_single sortable-element" data-image-id="${file.id}">
                                    <img alt="Preview ${index}" src="{{ asset('website') }}/${file.url}" loading="lazy">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item make_cover_btn" data-image-id="${file.id}" href="#">{{ translate('stepper.make_cover_photo') }}</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item delete_btn" data-image-id="${file.id}" href="#">{{ translate('stepper.delete') }}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>`
                            );
                            const addPhotoBox = $(
                                '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single.add_photo_box'
                            );
                            newDiv.insertBefore(addPhotoBox);
                        });
                        $('.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single:first-child')
                            .removeClass('sortable-element');
                        imageValidation();

                        Swal.fire({
                            title: @json(translate('stepper.upload_complete')),
                            text: @json(translate('stepper.all_images_have_been_uploaded_successfully')),
                            icon: "success",
                            timer: 1500,
                            showConfirmButton: false
                        });

                        $('#add_photo_file').val('');


                    } else {
                        Swal.fire({
                            title: @json(translate('stepper.error')),
                            text: response.message,
                            icon: "error"
                        });

                        $('#add_photo_file').val('');

                        imageValidation();

                        return;
                    }

                },
                // complete: function() {
                //     Swal.fire({
                //         title: "Upload Complete",
                //         text: "All images have been uploaded successfully!",
                //         icon: "success",
                //         timer: 1500,
                //         showConfirmButton: false
                //     });
                // },
                error: function(xhr) {
                    Swal.fire({
                        title: @json(translate('stepper.error')),
                        text: @json(translate('stepper.something_went_wrong')),
                        icon: "error"
                    });
                },
            });
        });
        // Image upload end

        // Delete image
        $(document).on('click',
            '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu .delete_btn',
            function() {
                let listing_image = $(this);
                let imageId = listing_image.data('image-id');
                var listing_id = $("input[name='listing_id']").val();

                Swal.fire({
                    title: @json(translate('stepper.are_you_sure')),
                    // text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: @json(translate('stepper.yes_delete'))
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('listing_image_delete') }}",
                            type: "DELETE",
                            headers: {
                                "X-CSRF-TOKEN": "{{ csrf_token() }}",
                            },
                            data: {
                                image_id: imageId,
                                listing_id
                            },
                            success: function(response) {
                                if (response.status == true) {
                                    listing_image.closest('.drag_drop_photo_single').remove();
                                    var imageNumber = $(
                                        '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single'
                                    ).length;
                                    if (imageNumber >= 6) {
                                        $('.listing_stepper .add_photos_step:not(.add_documents_step) .next.action-button')
                                            .prop('disabled',
                                                false);
                                    } else {
                                        $('.listing_stepper .add_photos_step:not(.add_documents_step) .next.action-button')
                                            .prop('disabled',
                                                true);
                                    }
                                    Swal.fire({
                                        title: @json(translate('stepper.deleted')),
                                        text: @json(translate('stepper.your_file_has_been_deleted')),
                                        icon: "success"
                                    });
                                }
                            }
                        });
                    }
                });


            });
        // Delete image end

        // Cover Image
        $(document).on('click',
            '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu .make_cover_btn',
            function() {
                let image_holder = $(this)
                var currentImage = image_holder.closest('.drag_drop_photo_single').html();
                $.ajax({
                    url: "{{ route('listing_cover_image') }}",
                    type: "POST",
                    headers: {
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    },
                    data: {
                        image_id: $(this).data('image-id'),
                        listing_id: $("input[name='listing_id']").val()
                    },
                    success: function(response) {
                        if (response.status == true) {
                            var currentCoverImage = $(
                                '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper .drag_drop_photo_single:first-child'
                            ).html();
                            $('.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper .drag_drop_photo_single:first-child')
                                .html(currentImage);
                            image_holder.closest('.drag_drop_photo_single').html(currentCoverImage);
                        } else {
                            Swal.fire({
                                title: @json(translate('stepper.error')),
                                text: @json(translate('stepper.something_went_wrong')),
                                icon: "error"
                            });
                        }
                    }
                });
            });
        // Cover Image End
    
        $(document).ready(function() {
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];
            const validExtensions = ['.jpg', '.jpeg', '.png', '.heif', '.heic'];
            const minSize = 300 * 1024;
            const maxSize = 10 * 1024 * 1024; // 10MB
            const maxFiles = 10; // number of images per upload
            let validImages = []; // Store valid images
            let validImageCount = 0;

            // Helper function to check if file is valid
            const isValidImageFile = (file) => {
                // First check MIME type
                if (file.type && validTypes.includes(file.type)) {
                    return true;
                }

                // Fallback: check file extension for files with empty/unknown MIME type (like HEIC)
                const fileName = file.name.toLowerCase();
                return validExtensions.some(ext => fileName.endsWith(ext));
            };

            $('#imageUpload').on('change', async function() {
                let files = Array.from(this.files);
                files = await handleFiles(files);
                // Check if user is uploading more than 10 images
                if (files.length > maxFiles) {
                    toastr.error(@json(translate('stepper.you_can_only_upload_a_maximum_of_images_at_a_time')));
                    this.value = ""; // Clear input field
                    return;
                }
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    console.log(`File: ${file.name}, Type: "${file.type}", Size: ${file.size}`);
                    // Validate file type
                    if (!isValidImageFile(file)) {
                        // toastr.error(`❌ Invalid file type: ${file.name}`);
                        previewImage(file, @json(translate('stepper.invalid_file_type')));
                        continue;
                    }
                    // Validate file size
                    if (file.size < minSize || file.size > maxSize) {
                        // toastr.error(`⚠️ File size not allowed: ${file.name} (must be 300KB-10MB)`);
                        previewImage(file, @json(translate('stepper.file_size_not_allowed')));
                        continue;
                    }
                    // Store only valid files
                    validImages.push(file);
                    previewImage(file);
                }
                // Update valid image count
                validImageCount = validImages.length;
                $("#validImageCount").text(validImageCount);
                this.value = "";
                // setTimeout(function() {
                //     addBtn();
                // }, 200);
            });

            // modal open
            $(".photo_upload_btn").on("click", function() {
                validImageCount = 0;
                validImages = [];
                $("#imagePreview").empty();
                $(".image-upload_wrapper").show();
                $("#validImageCount").text(validImageCount);
                $('#image-uploader-modal').modal("show")
            });

            


            // HEIC image format preview script starts from here

            async function resizeImage(file, maxWidth = 600, maxHeight = 600) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            try {
                                const canvas = document.createElement("canvas");
                                const ctx = canvas.getContext("2d");
                                const ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
                                canvas.width = img.width * ratio;
                                canvas.height = img.height * ratio;
                                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                                resolve(canvas.toDataURL("image/jpeg", 0.9));
                            } catch (error) {
                                console.error("Canvas error:", error);
                                resolve(e.target.result);
                            }
                        };
                        img.onerror = function() {
                            console.error("Image load error:", file.name);
                            resolve(e.target.result);
                        };
                        img.src = e.target.result;
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            }

            async function processImageFile(file) {
                const fileName = file.name.toLowerCase();
                const isHeic = fileName.endsWith('.heic') || fileName.endsWith('.heif') || file.type === 'image/heic' || file.type === 'image/heif';
                let processedFile = file;

                if (isHeic && window.heic2any) {
                    try {
                        const convertedBlob = await heic2any({
                            blob: file,
                            toType: 'image/jpeg',
                            quality: 0.8
                        });
                        processedFile = new File([convertedBlob], file.name.replace(/\.[^/.]+$/, '.jpg'), {
                            type: 'image/jpeg',
                            lastModified: Date.now()
                        });
                    } catch (err) {
                        console.error("HEIC conversion failed:", err);
                        throw new Error("Failed to convert HEIC image");
                    }
                }

                const imageUrl = await resizeImage(processedFile);
                return { imageUrl, processedFile };
            }


            // HEIC image format preview script ends here


            async function previewImage(file, message = "") {
                try {
                    const validExtensions = ['.jpg', '.jpeg', '.png', '.heif', '.heic'];
                    const fileName = file.name.toLowerCase();
                    const isHeic = validExtensions.some(ext => fileName.endsWith(ext));
                    
                    if (isHeic && !window.heic2any) {
                        await loadHeic2AnyLibrary();
                    }

                    const { imageUrl } = await processImageFile(file);

                    const errorMsg = message ? `<p class="error-message">${message}</p>` : "";
                    const img = $(`
                        <div class="image-preview col-md-6">
                            <img src="${imageUrl}" alt="${file.name}">
                            <span class="remove-image" data-name="${file.name}"><i class="fa fa-trash" aria-hidden="true"></i></span>
                            ${errorMsg} 
                        </div>`);
                    $('#imagePreview').append(img);
                    updateImageUploadWrapperVisibility();
                } catch (error) {
                    console.error("Preview failed:", error);
                    const fallbackUrl = URL.createObjectURL(file);
                    const img = $(`
                        <div class="image-preview col-md-6">
                            <img src="${fallbackUrl}" alt="${file.name}">
                            <span class="remove-image" data-name="${file.name}"><i class="fa fa-trash" aria-hidden="true"></i></span>
                            <p class="error-message">Failed to preview image</p>
                        </div>`);
                    $('#imagePreview').append(img);
                    updateImageUploadWrapperVisibility();
                }
            }



            // Made changes in the below function

            // function previewImage(file, message = "") {
            //     const reader = new FileReader();
            //     reader.onload = function(e) {
            //         const errorMsg = message ? `<p class="error-message">${message}</p>` : "";
            //         const img = $(`
            //             <div class="image-preview col-md-6">
            //                 <img src="${e.target.result}" alt="${file.name}">
            //                 <span class="remove-image" data-name="${file.name}"><i class="fa fa-trash" aria-hidden="true"></i></span>
            //                 ${errorMsg} 
            //             </div>`);
            //         $('#imagePreview').append(img);
            //         updateImageUploadWrapperVisibility();
            //     }
            //     reader.readAsDataURL(file);
            // }

            // This function ends here

            // Handle remove image event
            $('#imagePreview').on('click', '.remove-image', function() {
                const fileName = $(this).data("name");
                // Remove from validImages array
                validImages = validImages.filter(img => img.name !== fileName);
                // Update image count
                validImageCount = validImages.length;
                $("#validImageCount").text(validImageCount);
                $(this).parent().remove();
                updateImageUploadWrapperVisibility();
                addBtn();
            });
            const updateImageUploadWrapperVisibility = () => {
                if ($('#imagePreview').children().length >= 1) {
                    $(".image-upload_wrapper").hide();
                } else {
                    $(".image-upload_wrapper").show();
                }
            }

            // Handle Upload Button Click
            $('#uploadImages').on('click', function() {
                if (validImages.length === 0) {
                    toastr.error(@json(translate('stepper.no_valid_images_to_upload')));
                    return;
                }
                let formData = new FormData();
                validImages.forEach((file, index) => {
                    formData.append("media[]", file); // Append images to FormData
                    formData.append("listing_id", $("input[name='listing_id']").val());
                    formData.append("type", "image");
                    console.log("file", file);
                    console.log("index", index);
                });
                console.log("formData", formData);
                let uploadedCount = 0;
                const totalFiles = validImages.length;
                Swal.fire({
                    title: @json(translate('stepper.uploading_images')),
                    html: `{{ translate('stepper.uploading') }} <b>0</b> {{ translate('stepper.out_of') }} ${totalFiles}`,
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                });
                $.ajax({
                    url: "{{ route('listingMedia') }}",
                    type: "POST",
                    headers: {
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    },
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhr: function() {
                        let xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function(evt) {
                            if (evt.lengthComputable && Swal.isVisible()) {
                                let percentComplete = Math.round((evt.loaded / evt
                                    .total) * 100);
                                uploadedCount = Math.round((percentComplete / 100) *
                                    totalFiles); // Estimate uploaded files
                                uploadedCount = Math.min(uploadedCount,
                                    totalFiles); // Ensure it doesn't exceed totalFiles

                                Swal.update({
                                    html: `{{ translate('stepper.uploading') }} <b>${uploadedCount}</b> {{ translate('stepper.out_of') }} ${totalFiles}...`,
                                });
                            }
                        }, false);
                        return xhr;
                    },
                    success: function(response) {
                        imageValidation();
                        if (response.status == true) {
                            $.each(response.data, function(index, file) {
                                uploadedCount++;
                                Swal.update({
                                    html: `{{ translate('stepper.uploading') }} <b>${uploadedCount}</b> {{ translate('stepper.out_of') }} ${totalFiles}...`,
                                });
                                const newDiv = $(
                                    `<div class="drag_drop_photo_single sortable-element" data-image-id="${file.id}">
                                    <img alt="Preview ${index}" src="{{ asset('website') }}/${file.url}">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item make_cover_btn" data-image-id="${file.id}" href="#">@json(translate('stepper.make_cover_photo'))</a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item delete_btn" data-image-id="${file.id}" href="#">@json(translate('stepper.delete'))</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>`
                                );
                                const addPhotoBox = $(
                                    '.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single.add_photo_box'
                                );
                                newDiv.insertBefore(addPhotoBox);
                                // toastr.success("✅ Images uploaded successfully!");
                                validImages = []; // Clear stored images
                                $("#imagePreview").html(""); // Clear UI
                                $("#validImageCount").text("0"); // Reset count
                            });
                            $('.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photo_single:first-child')
                                .removeClass('sortable-element');
                            imageValidation();
                            $('#image-uploader-modal').modal("hide")
                            Swal.fire({
                                title: @json(translate('stepper.upload_complete')),
                                text: @json(translate('stepper.all_images_have_been_uploaded_successfully')),
                                icon: "success",
                                timer: 1500,
                                showConfirmButton: false
                            });
                            $('#add_photo_file').val('');
                        } else {
                            Swal.fire({
                                title: @json(translate('stepper.error')),
                                text: response.message,
                                icon: "error"
                            });
                            $('#add_photo_file').val('');
                            imageValidation();
                            return;
                        }
                    },
                    error: function(xhr) {
                        toastr.error(@json(translate('stepper.error_uploading_images')));
                    },
                });
            });
        });
    </script>
@endpush
