<div class="col-md-12 listing_custom_meta divider listing_data">
    <ul class="list-unstyled d-flex gap-3 m-0 parent-box">
        <li class="catg box d-flex gap-2 align-items-center text-capitalize" data-aos="fade">
            {{ translateDynamic($listing->detail->capacity) ?? '0' }} {{ translate('listing_details.passengers') }}
        </li>
        @if (!empty($listing->detail->boat_length) && $listing->detail->boat_length != 0)
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                {{ translateDynamic($listing->detail->boat_length) ?? '0' }} {{ translate('listing_details.ft') }}
            </li>
        @endif
        @if ($listing->detail->basis_type == 'Daily')
            @isset($listing->detail->check_in_time)
                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                    {{ translate('listing_details.starts_at') }} {{ $listing->detail->check_in_time ?? '- -' }}
                </li>
            @endisset
            @isset($listing->detail->check_out_time)
                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                    {{ translate('listing_details.ends_at') }} {{ $listing->detail->check_out_time ?? '- -' }}
                </li>
            @endisset
        @endif
        @isset($listing->activity)
            @foreach ($listing->activity as $activity)
                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                    {{ translateDynamic($activity->name) }}
                </li>
            @endforeach
        @endisset
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
            @if ($listing->detail->pet == 'yes')
                {{ translate('listing_details.pets_allowed') }}
            @else
                {{ translate('listing_details.pets_not_allowed') }}
            @endif
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            {{-- <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px"> --}}
            @if ($listing->detail->boat_captain == 'yes')
                <img src="{{ asset('website/images/captain.png') }}" alt="" height="20px" width="20px">
                {{ translate('listing_details.captain_included') }}
            @else
                <img src="{{ asset('website/images/captain.png') }}" alt="" height="20px" width="20px">
                {{ translate('listing_details.captain_not_included') }}
            @endif
        </li>
    </ul>
</div>
{{-- Amenities --}}
@if (isset($listing->amenity_detail[0]))
    <div class="col-lg-12 divider listing_data listing_amenities">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.everything_included_with_this_watercraft') }}</h3>
            <div class="parent-box row g-0 align-items-center">
                @foreach ($listing->amenity_detail as $key => $amenities)
                    @if ($key < 6)
                        <div class="col-md-3 col-sm-12">
                            <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                                <img src="{{ asset('website') . '/' . $amenities->image }}" alt=""
                                    height="30px" width="30px">
                                <div class="amenity-data">
                                    <span>{{ translateDynamic(ucfirst($amenities->name)) ?? '' }}</span>
                                    @isset($amenities->description)
                                        <p class="amenties_desc fs-14 text-black-50">
                                            {{ translateDynamic($amenities->description) ?? '' }}
                                        </p>
                                    @endisset
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
            {{-- @if (count($listing->amenity_detail) > 16) --}}
            <button type="button" class="button button1 mt-3" data-bs-toggle="modal" data-bs-target="#all-amenties">
                {{ translate('listing_details.show_all') }} {{ count($listing->amenity_detail) }} {{ translate('see_all_expansion.boat_view_btn') }}
            </button>
            {{-- @endif --}}
        </div>
    </div>
    <div class="modal fade all-amenties" id="all-amenties" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content pb-0">
                <div class="modal-header border-0 ">
                    <h4 class="modal-title mx-auto">{{ translate('listing_details.everything_included_with_this_watercraft') }}</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pb-lg-10 px-lg-10 pt-4 pb-0">
                    <div class="parent-box row g-0 align-items-start">
                        @foreach ($listing->amenity_detail as $amenities)
                            <div class="col-md-12 divider pb-3">
                                <div class="box d-flex gap-3 align-items-center" data-aos="fade">
                                    <img src="{{ asset('website') . '/' . $amenities->image }}" alt=""
                                        height="30px" width="30px">
                                    <div class="amenity-data ">
                                        <span>{{ translateDynamic($amenities->name) ?? '' }}</span>
                                        @isset($amenities->translations->firstWhere('locale', session('locale', 'en'))->description)
                                            <p class="amenties_desc fs-14 text-black-50">
                                                {{ $amenities->translations->firstWhere('locale', session('locale', 'en'))->description ?? '' }}
                                            </p>
                                        @endisset
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
{{-- End Amenities --}}
{{-- Equipment --}}
@if (in_array($listing->category_id, [2]))
    @if (isset($listing->includes[0]))
        <div class="col-lg-12 divider listing_data listing_equipment">
            <div class="amenities-box">
                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.included_equipment') }}</h3>
                <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                    @foreach ($listing->includes as $include)
                        <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/square-check.svg') }}" height="20px" width="20px"
                                alt="">
                            <span>{{ translateDynamic($include->name) }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
    @if (isset($listing->not_includes[0]))
        <div class="col-lg-12 divider listing_data listing_equipment">
            <div class="amenities-box">
                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.not_included') }}</h3>
                <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                    @foreach ($listing->not_includes as $not_include)
                        <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                            <img src="{{ asset('website/images/ticksquare.svg') }}" height="20px" width="20px"
                                alt="">
                            <span>{{ translateDynamic($not_include->name) }}</span>
                        </div>
                    @endforeach
                </div>

            </div>
        </div>
    @endif
@endif
{{-- End Equipment --}}
{{-- Key and feature --}}
@if (in_array($listing->category_id, [2, 3, 4]))
    @if (isset($listing->key_features[0]))
        <div class="col-lg-12 divider listing_data listing_key_feature">
            <div class="key_features">
                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.key_features') }}</h3>
                <div class="parent-feature parent-box row g-0 gap-3 align-items-start">
                    @foreach ($listing->key_features as $key_feature)
                        <div class="box col-md-3" data-aos="fade">
                            <h6 class="fs-16 semi-bold">{{ translateDynamic($key_feature->title) }}</h6>
                            <p class="fs-12">{!! translateDynamic($key_feature->description) !!}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
@endif
{{-- End Key and feature --}}
