@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "detail-step");
@endphp
<fieldset class="basic_accommodation_step basic_vehicle_step accommodations_rules_step basics_experience_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                        @isset($step_data->sub_title)
                            <p class="sub_title">{{ $step_data->sub_title ?? "" }}</p>
                        @endisset
                    </div>
                    <div class="space_details_wrapper">
                        <div class="space_detail_single">
                            <div class="space_detail_title">
                                <label for="seats">{{ translate('stepper.seats') }}  </label>
                            </div>
                            <div class="space_detail_quantity_wrapper">
                                <button class="btn minus_btn" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                    <i class="fa fa-minus" aria-hidden="true"></i>
                                </button>
                                <input type="number" min="1" value="{{ $listing->detail->seats ?? '1' }}"
                                    class="form-control file" name="seats" id="seats" required />
                                <button class="btn plus_btn" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>

                        <div class="space_detail_single" style="border-bottom: 0;">
                            <div class="allow_pets_field_wrapper allow_children_field_wrapper mt-0">
                                <div class="allow_pets_title">
                                    <h4  class=" text-start">{{ translate('stepper.vehicle_chauffeur') }}</h4>
                                </div>
                                <div class="allow_pets_input_wrapper">
                                    <div class="allow_pets_input">
                                        <label for="chauffeur_yes">{{ translate('stepper.yes') }}</label>
                                        <input type="radio" class="radio_btn toggle" id="chauffeur_yes" value="yes"
                                            {{ ($listing->detail->chauffeur ?? '') == 'yes' ? 'checked' : '' }}
                                            name="chauffeur" />
                                    </div>
                                    <div class="allow_pets_input">
                                        <label for="chauffeur_no">{{ translate('stepper.no') }}</label>
                                        <input type="radio" class="radio_btn toggle" id="chauffeur_no" value="no"
                                            {{ ($listing->detail->chauffeur ?? 'no') == 'no' ? 'checked' : '' }}
                                            name="chauffeur" />
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    {{-- <div class="space_details_wrapper">
                        <div class="space_detail_single">
                            <div class="space_detail_title">
                                <label for="seats">{{ translate('stepper.chauffeur') }}  </label>
                            </div>
                            <div class="space_detail_quantity_wrapper">
                                <div>
                                    <label for="chauffeur_yes">{{ translate('stepper.yes') }}</label>
                                    <input type="radio" name="chauffeur" {{ ($listing->detail->chauffeur ?? '') == 'yes' ? 'checked' : '' }} value="yes" id="chauffeur_yes">
                                </div>

                                <div>
                                    <label for="chauffeur_no">{{ translate('stepper.no') }}</label>
                                    <input type="radio" name="chauffeur" {{ ($listing->detail->chauffeur ?? '') == 'no' ? 'checked' : '' }} value="no" id="chauffeur_no">
                                </div>
                            </div>
                        </div>
                    </div> --}}

                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>
