<?php

namespace App\Services;

use App\Booking;
use App\Mail\BookingCreateMail;
use App\Models\BookingDetail;
use App\Models\BookingDiscount;
use App\Models\BookingHourSlot;
use App\Models\BookingTourDuration;
use App\Models\User;
use App\Notifications\SuspendUserNotification;
use Illuminate\Support\Facades\DB;
use App\Services\PayPalService;
use App\Traits\BookingTrait;
use Illuminate\Support\Facades\Mail;
use Stripe\Charge;
use Carbon\Carbon;

class BookingService
{
    use BookingTrait;
    function __construct(

        protected PayPalService $paypalService,
        protected StripeService $stripeService,
        protected CancellationPolicyService $cancellationPolicyService,
        protected WalletService $walletService
    ) {}

    // Get customer booking by status
    function getBookingByStatus($status, $perPage = 10)
    {
        try {
            $query = Booking::query()
                ->with([
                    'listing' => function ($query) {
                        $query->withTrashed();
                    },
                    'listing.user' => function ($query) {
                        $query->withTrashed();
                    }
                ])
                ->where('user_id', auth()->id())
                ->orderByDesc('id');
            // Adjust query based on status
            if ($status == 'past') {
                $query->where('status', '!=', 0);
            } elseif ($status == 'current') {
                $query->where('status', '=', 0);
            }
            $bookings = $query->paginate($perPage);
            return $bookings;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    function create_booking($data, $listing, $paymentIntentId, $chargeId, $retrieve_charge = null)
    {
        try {
            DB::beginTransaction();
            $basis_type = $listing->detail->basis_type;
            $listing_price = season_price($data["check_in"], $listing->base_price, $listing->seasons);
            // Total amount calculation
            if ($basis_type == "Hourly") {
                $totalAmount = $listing_price * $data["total_hours"] * $data["total_days"];
            } elseif ($basis_type == "Daily") {
                $totalAmount = $listing_price * $data["total_days"];
            } elseif ($basis_type == "Tour") {
                if ($data["tour_type"] == "guests") {
                    $adult_price = season_price($data["check_in"], $listing->detail->adult_price, $listing->seasons);
                    $child_price = season_price($data["check_in"], $listing->detail->child_price, $listing->seasons);
                    $totalAmount = ($adult_price * $data["adult_number"]) + ($child_price * $data['child_number']);
                } else {
                    $private_booking_price = $listing->detail->private_booking_price;
                    $totalAmount = $listing->detail->private_booking_price;
                }
            }
            $sub_total = $totalAmount;
            // Total amount calculation end

            // weekly / monthly discount
            $weekly_monthly_discount = weeklyMonthlyDiscount($data["total_days"], $totalAmount, $listing->discount);
            if (isset($weekly_monthly_discount["discount_amount"])) {
                $sub_total -= $weekly_monthly_discount["discount_amount"];
            }
            // weekly / monthly discount end
            // new listing discount
            $new_listing_discount = newListingDiscount($totalAmount, $listing->bookings()->count(), $listing->discount);
            if (isset($new_listing_discount["discount_amount"])) {
                $sub_total -= $new_listing_discount["discount_amount"];
            }
            // new listing discount end
            $booking = new Booking();
            $booking->booking_number = "LUX-" . rand(100000, 999999);
            $booking->user_id =  auth()->id();
            $booking->listing_id = $listing->id;
            $booking->provider_id = $listing->user_id;
            $booking->currency = $data["currency"];
            $booking->conversion_rate = $data["conversion_rate"];
            $booking->cancellation_policy = $listing->detail->cancellation_policy ?? '';
            $booking->check_in = $data["check_in"];
            if (!$data["check_out"] || $data["check_out"] == 0) {
                $booking->check_out = $data["check_in"];
            } else {
                $booking->check_out = $data["check_out"];
            }
            if (in_array($listing->category_id, [3])) {
                $booking->check_in_time = $data["check_in_time"] ?? "";
                $booking->check_out_time = $data["check_out_time"] ?? "";
            }
            $booking->guest = $data["guests"] ?? $data["guest"] ?? null;
            $booking->listing_basis = $basis_type;
            if ($basis_type == "Tour") {
                $booking->tour_type = $data["tour_type"];
                if ($data["tour_type"] == "guests") {
                    $booking->adult_price = $adult_price;
                    $booking->child_price = $child_price;
                } else {
                    $booking->private_booking_price = $private_booking_price ?? null;
                }
                $booking->guest = ($data["child_number"] ?? 0) + ($data["adult_number"] ?? 1);
            } else {
                $booking->listing_price = $listing_price;
            }
            $booking->total_amount = $totalAmount;
            $booking->total_usd_amount = $data["usd_conversion_amount"] ?? 0;
            $booking->sub_total = $sub_total;
            $booking->payment_intent_id = $paymentIntentId;
            $booking->payment_method = $data["payment-method"];
            $booking->receipt_url =  $retrieve_charge["data"]["receipt_url"] ?? null;
            $booking->card_brand =  $retrieve_charge['data']['payment_method_details']['card']['brand'] ?? null;
            $booking->last_4_digits =  $retrieve_charge['data']['payment_method_details']['card']['last4'] ?? null;
            $booking->charge_id = $chargeId;
            $booking->save();
            if ($booking) {
                $booking_detail = new BookingDetail();
                $booking_detail->booking_id = $booking->id;
                $booking_detail->total_days = $data["total_days"];
                $booking_detail->total_hours = $basis_type == "Hourly" ? $data["total_hours"] : null;
                $booking_detail->adult_number = $basis_type == "Tour" ? $data["adult_number"] : null;
                $booking_detail->child_number = $basis_type == "Tour" ? $data["child_number"] : null;
                if ($booking->listing_basis == "Tour") {
                    $booking_detail->tour_duration_type = $listing->detail->tour_day_type == "same_day" ? "Same Day" : "Multiple Days";
                    if ($listing->detail->tour_day_type == "same_day") {
                        $booking_tour_duration = new BookingTourDuration();
                        $booking_tour_duration->booking_id = $booking->id;
                        $booking_tour_duration->day = 1;
                        $booking_tour_duration->start_time = $listing->detail->start_time;
                        $booking_tour_duration->end_time = $listing->detail->end_time;
                        $booking_tour_duration->save();
                    } else {
                        foreach ($listing->tour_durations as $tour_date) {
                            $booking_tour_duration = new BookingTourDuration();
                            $booking_tour_duration->booking_id = $booking->id;
                            $booking_tour_duration->day = $tour_date->day;
                            $booking_tour_duration->start_time = $tour_date->start_time;
                            $booking_tour_duration->end_time = $tour_date->end_time;
                            $booking_tour_duration->save();
                        }
                    }
                }
                $booking_detail->save();

                // hour slot
                if ($basis_type == "Hourly") {
                    foreach ($data["hour_slots"] as $hour_slot) {
                        $booking_hour_slot = new BookingHourSlot();
                        $booking_hour_slot->booking_id = $booking->id;
                        $booking_hour_slot->listing_id = $booking->listing_id;
                        $booking_hour_slot->date = $booking->check_in;
                        $booking_hour_slot->slot = $hour_slot;
                        $booking_hour_slot->save();
                    }
                }
            }

            // weekly and monthly discount
            if (isset($weekly_monthly_discount["discount_amount"], $weekly_monthly_discount["discount_percentage"], $weekly_monthly_discount["discount_name"])) {
                $booking_discount = new BookingDiscount();
                $booking_discount->booking_id = $booking->id;
                $booking_discount->type = $weekly_monthly_discount["discount_name"];
                $booking_discount->percent = $weekly_monthly_discount["discount_percentage"];
                $booking_discount->amount = $weekly_monthly_discount["discount_amount"];
                $booking_discount->save();
            }
            // weekly and monthly discount end

            // new listing 
            if (isset($new_listing_discount["discount_percentage"], $new_listing_discount["discount_percentage"], $new_listing_discount["discount_amount"])) {
                $booking_discount = new BookingDiscount();
                $booking_discount->booking_id = $booking->id;
                $booking_discount->type = $new_listing_discount["discount_name"];
                $booking_discount->percent = $new_listing_discount["discount_percentage"];
                $booking_discount->amount = $new_listing_discount["discount_amount"];
                $booking_discount->save();
            }
            // new listing end

            // Mail send 
            Mail::to($booking->customer->email)->send(new BookingCreateMail($booking));

            DB::commit();
            return api_response(true, "Booking Created Successfully", $booking);
        } catch (\Exception $e) {
            DB::rollback();
            return api_response(false, $e->getMessage());
        };
    }

    function cancel_booking($booking_id)
    {
        DB::beginTransaction();
        try {
            $booking = Booking::where("ids", $booking_id)->where("status", 0)->first();
            if ($booking) {
                if ($booking->check_in <= today()) {
                    return api_response(false, "Cancellations are restricted after check-in.");
                }
                $cancellationPolicyService = $this->cancellationPolicyService->calculateCancellationAmount($booking);
                if (($cancellationPolicyService["status"] ?? null) == false) {
                    return api_response(false, isset($cancellationPolicyService["message"]));
                }
                // $service_provider_amount = $cancellationPolicyService["data"]["service_provider_amount"];
                $customer_refund_amount = $cancellationPolicyService["data"]["customer_amount"];
                if ($customer_refund_amount > 0) {
                    if ($booking->payment_method == 'card') {
                        $stripeService = $this->stripeService->refund($booking->charge_id, $customer_refund_amount);
                        if (isset($stripeService['status']) && $stripeService['status'] == false) {
                            return api_response(false, $stripeService['message']);
                        }
                    } elseif ($booking->payment_method == 'paypal') {
                        $stripeService = $this->paypalService->refund($booking->charge_id, $customer_refund_amount);
                        if (isset($stripeService['status']) && $stripeService['status'] == false && !isset($stripeService['data']["id"])) {
                            return api_response(false, $stripeService['message']);
                        }
                    } else {
                        return api_response(false, "Booking not found");
                    }
                    $refund = $stripeService["data"];
                    $booking->refund_id = $refund['id'];
                }
                $booking->refund_amount = $customer_refund_amount;
                $booking->refund_percentage = $cancellationPolicyService["data"]["refundPercentage"];
                $booking->service_refund_amount_cop = $cancellationPolicyService["data"]["service_provider_amount"];
                $booking->status = 7;
                $booking->save();
                if ($booking->listing_basis == "Hourly") {
                    BookingHourSlot::where('booking_id', $booking->id)->update(['status' => 7]);
                }
                DB::commit();
                return api_response(true, "Your booking has been cancelled", $booking);
            } else {
                return api_response(false, "Booking not found");
            }
        } catch (\Exception $e) {
            DB::rollback();
            return api_response(false, $e->getMessage());
        }
    }
    function cancel_bookings($bookings)
    {
        try {
            return DB::transaction(function () use ($bookings) {
                foreach ($bookings as $booking) {
                    $cancel_booking = $this->cancel_booking($booking["ids"]);
                    if ($cancel_booking["status"] == false) {
                        return api_response(false, $cancel_booking["message"]);
                    }
                }
                return api_response(true, "Bookings Cancelled");
            });
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }

    function adminCancelBooking($request_data)
    {
        //try {
        return DB::transaction(function () use ($request_data) {

            $booking = Booking::where("ids", $request_data["booking_id"])
                ->where("status", 0)
                ->first();

            if (!$booking) {
                return api_response(false, "Booking not found");
            }

            if (!auth()->user()->hasRole("user")) {
                if ($booking->check_in <= today()) {
                    return api_response(false, "Booking cant be cancelled after check-in");
                }
            }

            if ($request_data["deductProvider"] == "yes") {
                $deductAmount = $this->walletService->walletDeductAmount($booking->provider_id, $booking->sub_total);
                if ($deductAmount["status"] == false) {
                    return api_response(false, "Deduct Amount Failed");
                }
            }

            $calculateCancellationAmount = get_paypal_percentage($request_data["refundValue"], $booking->total_usd_amount);
            $cop_calculateCancellationAmount = get_paypal_percentage($request_data["refundValue"], $booking->total_amount);
            if ($request_data["refundValue"] > 0) {
                if ($request_data['payment_method'] == 'card') {
                    $refund = $this->stripeService->refund($booking->charge_id, $calculateCancellationAmount);
                    if (isset($refund['status']) && $refund['status'] == false && !isset($refund['data']["id"])) {
                        return api_response(false, $refund['message']);
                    }
                    $booking->refund_id = $refund['data']["id"];
                    $booking->refund_amount = $cop_calculateCancellationAmount;
                    $booking->refund_percentage = $request_data["refundValue"];
                    $booking->status = 7;
                    $booking->save();
                } elseif ($request_data['payment_method'] == 'paypal') {
                    //return $calculateCancellationAmount;
                    $refund = $this->paypalService->refund($booking->charge_id, $calculateCancellationAmount);
                    // if (isset($refund['status']) && $refund['status'] == false && !isset($refund['data']["id"])) {
                    //     return api_response(false, $refund['message']);
                    // }
                    //return $refund;
                    if ($refund['status'] === true) {
                        $booking->refund_id = $refund['data']['id'];
                        $booking->refund_amount = $cop_calculateCancellationAmount;
                        $booking->refund_percentage = $request_data["refundValue"];
                        $booking->status = 7; // Refunded status
                        $booking->save();
                    } else {
                        return api_response(false, $refund['message']);
                    }
                } else {
                    return api_response(false, 'Unsupported payment method');
                }
            } else {
                $booking->refund_amount = 0;
                $booking->refund_percentage = 0;
                $booking->status = 7;
                $booking->save();
            }
            return api_response(true, "Booking Cancelled", $booking);
        });
        // } catch (\Exception $e) {
        //     return api_response(false, $e->getMessage());
        // }
    }

    function activeBookings(string $type = "listing", $type_id)
    {
        try {
            if ($type == "listing") {
                $booking = Booking::with("provider:id,name", "customer:id,name")->where("listing_id", $type_id)->where("status", 0)->get();
            } elseif ($type == "customer") {
                $booking = Booking::with("provider:id,name", "customer:id,name")->where("user_id", $type_id)->where("status", 0)->get();
            } elseif ($type == "provider") {
                $provider = User::where("id", $type_id)->first();
                $booking = Booking::with("provider:id,name", "customer:id,name")->where("provider_id", $provider->id)->where("status", 0)->get();
            }
            return $booking;
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }

    /**
     * Get bookings with filters for admin dashboard
     *
     * @param Request $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getBookingsWithFilters($request)
    {
        // Grab inputs
        $status = $request->get('status');
        $keyword = $request->get('search');
        $dateRange = $request->get('date');
        $perPage = $request->get('per_page', 10);

        // Start building the query
        $query = Booking::query()
            ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots'])
            ->whereHas('listing', function($q) {
                // Only include bookings where the listing exists and is not deleted
                $q->whereNull('deleted_at');
            });

        // Apply status filters
        $query = $this->applyStatusFilter($query, $status);

        // Apply date range filter
        $query = $this->applyDateRangeFilter($query, $dateRange);

        // Apply user role based filtering
        $query = $this->applyUserRoleFilter($query);

        // Apply search keyword filter
        $query = $this->applySearchFilter($query, $keyword);

        // Apply comprehensive sorting
        $query = $this->applySorting($query);

        return $query->paginate($perPage)->appends($request->except('page'));
    }

    /**
     * Apply status filter to booking query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string|null $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyStatusFilter($query, $status)
    {
        if ($status === 'ongoing') {
            $today = Carbon::today()->toDateString();
            $currentTime = Carbon::now();

            // "Ongoing" means check_in <= now, check_out >= now, status=0
            $query->where(DB::raw('DATE(check_in)'), '<=', $today)
                ->where(DB::raw('DATE(check_out)'), '>=', $today)
                ->where('status', 0)
                ->where(function($q) use ($currentTime) {
                    // For Daily bookings, date check is sufficient
                    $q->where('listing_basis', '!=', 'Hourly')
                      ->orWhere(function($q2) use ($currentTime) {
                          // For Hourly bookings, check if current time is within any booked slot
                          $q2->where('listing_basis', 'Hourly')
                             ->whereHas('hourly_slots', function($q3) use ($currentTime) {
                                 $q3->where(function($q4) use ($currentTime) {
                                     // Parse slot times and check if current time falls within any slot
                                     $q4->whereRaw("
                                         TIME(?) BETWEEN
                                         TIME(SUBSTRING_INDEX(slot, ' - ', 1)) AND
                                         TIME(SUBSTRING_INDEX(slot, ' - ', -1))
                                     ", [$currentTime->format('H:i:s')]);
                                 });
                             });
                      });
                });

        } elseif ($status === 'upcoming') {
            $today = Carbon::today()->toDateString();
            $currentTime = Carbon::now();

            $query->where('status', 0)
                ->where(function($q) use ($today, $currentTime) {
                    // Include future date bookings
                    $q->where('check_in', '>', $today)
                      ->orWhere(function($q2) use ($today, $currentTime) {
                          // Include same-day hourly bookings with future slots
                          $q2->where(DB::raw('DATE(check_in)'), '=', $today)
                             ->where('listing_basis', 'Hourly')
                             ->whereHas('hourly_slots', function($q3) use ($currentTime) {
                                 // Has at least one future slot
                                 $q3->whereRaw("
                                     CASE
                                         WHEN TIME(SUBSTRING_INDEX(slot, ' - ', 1)) <= TIME(SUBSTRING_INDEX(slot, ' - ', -1))
                                         THEN CONCAT(date, ' ', SUBSTRING_INDEX(slot, ' - ', 1))
                                         ELSE CONCAT(date, ' ', SUBSTRING_INDEX(slot, ' - ', 1))
                                     END > ?
                                 ", [$currentTime]);
                             })
                             // Exclude if currently ongoing
                             ->whereDoesntHave('hourly_slots', function($q4) use ($currentTime) {
                                 $q4->whereRaw("
                                     TIME(?) BETWEEN
                                     TIME(SUBSTRING_INDEX(slot, ' - ', 1)) AND
                                     TIME(SUBSTRING_INDEX(slot, ' - ', -1))
                                 ", [$currentTime->format('H:i:s')]);
                             });
                      });
                      // Removed the problematic same-day daily bookings logic
                      // Daily bookings that start today should be considered "ongoing", not "upcoming"
                });
        } elseif ($status === 'today') {
            $today = Carbon::today()->toDateString();
            $query->where(DB::raw('DATE(check_in)'), '=', $today);
        } elseif ($status === 'tomorrow') {
            $tomorrow = Carbon::tomorrow()->toDateString();
            $query->where(DB::raw('DATE(check_in)'), '=', $tomorrow);
        } elseif ($status === 'completed') {
            $currentTime = Carbon::now();

            $query->where(function($q) use ($currentTime) {
                // Include bookings with status = 3 (officially completed)
                $q->where('status', 3)
                  ->orWhere(function($q2) use ($currentTime) {
                      // Also include hourly bookings where all slots have passed
                      $q2->where('status', 0)
                         ->where('listing_basis', 'Hourly')
                         ->whereDoesntHave('hourly_slots', function($q3) use ($currentTime) {
                             // Exclude bookings that have any future slots
                             $q3->whereRaw("
                                 CASE
                                     WHEN TIME(SUBSTRING_INDEX(slot, ' - ', -1)) <= TIME(SUBSTRING_INDEX(slot, ' - ', 1))
                                     THEN CONCAT(DATE_ADD(date, INTERVAL 1 DAY), ' ', SUBSTRING_INDEX(slot, ' - ', -1))
                                     ELSE CONCAT(date, ' ', SUBSTRING_INDEX(slot, ' - ', -1))
                                 END > ?
                             ", [$currentTime]);
                         });
                  });
            });
        } elseif ($status === 'cancelled') {
            $query->where('status', 7);
        }

        return $query;
    }

    /**
     * Apply date range filter to booking query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string|null $dateRange
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyDateRangeFilter($query, $dateRange)
    {
        if (!empty($dateRange)) {
            try {
                [$startDate, $endDate] = explode(' - ', $dateRange);
                $startDate = Carbon::createFromFormat('m/d/Y', trim($startDate))->startOfDay()->toDateString();
                $endDate = Carbon::createFromFormat('m/d/Y', trim($endDate))->endOfDay()->toDateString();

                // Filter bookings where check-in date is within the selected range
                // OR check-out date is within the selected range
                // OR booking spans the entire selected range (check-in before start and check-out after end)
                $query->where(function($q) use ($startDate, $endDate) {
                    $q->where(function($q1) use ($startDate, $endDate) {
                        $q1->where(DB::raw('DATE(check_in)'), '>=', $startDate)
                           ->where(DB::raw('DATE(check_in)'), '<=', $endDate);
                    })->orWhere(function($q2) use ($startDate, $endDate) {
                        $q2->where(DB::raw('DATE(check_out)'), '>=', $startDate)
                           ->where(DB::raw('DATE(check_out)'), '<=', $endDate);
                    })->orWhere(function($q3) use ($startDate, $endDate) {
                        $q3->where(DB::raw('DATE(check_in)'), '<=', $startDate)
                           ->where(DB::raw('DATE(check_out)'), '>=', $endDate);
                    });
                });
            } catch (\Exception $e) {
                // If date parsing fails, ignore the date filter
            }
        }

        return $query;
    }

    /**
     * Apply user role based filtering to booking query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyUserRoleFilter($query)
    {
        $user = auth()->user();

        if ($user->hasRole(['user', 'sub_admin'])) {
            // No extra condition for admin users
        } elseif ($user->hasRole('service')) {
            $query->where('provider_id', $user->id);
        } else {
            // Normal user
            $query->where('user_id', $user->id);
        }

        return $query;
    }

    /**
     * Apply search keyword filter to booking query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string|null $keyword
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applySearchFilter($query, $keyword)
    {
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('booking_number', 'LIKE', "%$keyword%")
                  ->orWhereHas('customer', function($q1) use ($keyword) {
                      $q1->where('first_name', 'LIKE', "%$keyword%")
                         ->orWhere('last_name', 'LIKE', "%$keyword%");
                  })
                  ->orWhereHas('listing', function($q2) use ($keyword) {
                      $q2->where('name', 'LIKE', "%$keyword%");
                  });
            });
        }

        return $query;
    }

    /**
     * Apply comprehensive sorting to booking query
     * Sorts bookings by status priority: Ongoing -> Upcoming -> Completed -> Cancelled
     * Within each status group, sorts by check-in date
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applySorting($query)
    {
        $today = Carbon::today()->toDateString();

        $currentTime = Carbon::now();

        return $query->orderByRaw("
            CASE
                -- 🔵 Ongoing bookings first (priority 0)
                WHEN (
                    status = 0 AND (
                        -- Daily bookings: check_in <= today <= check_out
                        (listing_basis != 'Hourly' AND DATE(check_in) <= ? AND DATE(check_out) >= ?) OR
                        -- Hourly bookings: current time is within any booked slot
                        (listing_basis = 'Hourly' AND EXISTS (
                            SELECT 1 FROM booking_hour_slots bhs
                            WHERE bhs.booking_id = bookings.id
                            AND TIME(?) BETWEEN TIME(SUBSTRING_INDEX(bhs.slot, ' - ', 1)) AND TIME(SUBSTRING_INDEX(bhs.slot, ' - ', -1))
                            AND bhs.date = ?
                        ))
                    )
                ) THEN 0

                -- 🟡 Upcoming bookings second (priority 1)
                WHEN (
                    status = 0 AND (
                        -- Future date bookings
                        DATE(check_in) > ? OR
                        -- Same-day hourly bookings with future slots only (not currently ongoing)
                        (listing_basis = 'Hourly' AND DATE(check_in) = ? AND EXISTS (
                            SELECT 1 FROM booking_hour_slots bhs
                            WHERE bhs.booking_id = bookings.id
                            AND CONCAT(bhs.date, ' ', SUBSTRING_INDEX(bhs.slot, ' - ', 1)) > ?
                        ) AND NOT EXISTS (
                            SELECT 1 FROM booking_hour_slots bhs
                            WHERE bhs.booking_id = bookings.id
                            AND TIME(?) BETWEEN TIME(SUBSTRING_INDEX(bhs.slot, ' - ', 1)) AND TIME(SUBSTRING_INDEX(bhs.slot, ' - ', -1))
                            AND bhs.date = ?
                        ))
                    )
                ) THEN 1

                -- 🟢 Completed bookings third (priority 2)
                WHEN status = 3 THEN 2

                -- 🔴 Cancelled bookings last (priority 3)
                WHEN status = 7 THEN 3

                -- Any other status gets priority 4
                ELSE 4
            END ASC,

            -- Within each status group, sort by nearest date to today first
            ABS(DATEDIFF(check_in, ?)) ASC,

            -- Secondary sort by booking number for consistency
            booking_number ASC
        ", [
            $today, $today, $currentTime->format('H:i:s'), $today, // ongoing params
            $today, $today, $currentTime->format('Y-m-d H:i:s'), $currentTime->format('H:i:s'), $today, // upcoming params
            $today // nearest date calculation param
        ]);
    }

    /**
     * Get bookings for CSV export
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getBookingsForCsvExport($request)
    {
        // Start building the query
        $query = Booking::query()
            ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots'])
            ->whereHas('listing', function($q) {
                // Only include bookings where the listing exists and is not deleted
                $q->whereNull('deleted_at');
            });

        // Apply filters using the same methods as the main query
        $status = $request->get('status');
        $dateRange = $request->get('date');
        $keyword = $request->get('search');

        $query = $this->applyStatusFilter($query, $status);
        $query = $this->applyDateRangeFilter($query, $dateRange);
        $query = $this->applyUserRoleFilter($query);
        $query = $this->applySearchFilter($query, $keyword);
        $query = $this->applySorting($query);

        return $query->get();
    }

    /**
     * Generate CSV export for bookings with role-based formatting
     *
     * @param \Illuminate\Http\Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportBookingsCsv($request)
    {
        $bookings = $this->getBookingsForCsvExport($request);
        $status = $request->get('status', 'all');
        $user = auth()->user();

        $filename = 'bookings_' . $status . '_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($bookings, $user) {
            $file = fopen('php://output', 'w');

            // Add CSV headers based on user role (matching the table structure)
            if (!$user->hasRole('service')) {
                // Admin/Sub-admin view
                fputcsv($file, [
                    'Booking ID',
                    'Listing',
                    'Customer',
                    'Category',
                    'Date From',
                    'Until',
                    'Amount',
                    'Status',
                    'Created At'
                ]);
            } else {
                // Service provider view
                fputcsv($file, [
                    'Booking ID',
                    'Listing',
                    'Customer',
                    'Category',
                    'Date From',
                    'Until',
                    'Total Paid by Customer',
                    'Total Payout',
                    'Booked',
                    'Status',
                    'Created At'
                ]);
            }

            // Add booking data
            foreach ($bookings as $booking) {
                if (!$user->hasRole('service')) {
                    // Admin/Sub-admin row structure
                    $row = [
                        $booking->booking_number ?? '',
                        $booking->listing->name ?? 'Deleted Listing',
                        ($booking->customer->first_name ?? '') . ' ' . ($booking->customer->last_name ?? ''),
                        $booking->listing->category->display_name ?? '-',
                        date(config('constant.date_format'), strtotime($booking->check_in)),
                        date(config('constant.date_format'), strtotime($booking->check_out == 0 ? $booking->check_in : $booking->check_out)),
                        'COP ' . (isset($booking->total_amount) ? number_format($booking->total_amount, 0) : '-'),
                        $this->getStatusName($booking),
                        $booking->created_at->format('Y-m-d H:i:s')
                    ];
                } else {
                    // Service provider row structure
                    $deductedAmount = ($booking->total_amount ?? 0) * (($booking->listing->category->tax ?? 0) / 100);
                    $totalPayout = $booking->total_amount - $deductedAmount;

                    $row = [
                        $booking->booking_number ?? '',
                        $booking->listing->name ?? 'Deleted Listing',
                        ($booking->customer->first_name ?? '') . ' ' . ($booking->customer->last_name ?? ''),
                        $booking->listing->category->display_name ?? '-',
                        date(config('constant.date_format'), strtotime($booking->check_in)),
                        date(config('constant.date_format'), strtotime($booking->check_out == 0 ? $booking->check_in : $booking->check_out)),
                        'COP ' . (isset($booking->total_amount) ? number_format($booking->total_amount, 0) : '-'),
                        'COP ' . (isset($totalPayout) ? number_format($totalPayout) : '-'),
                        date(config('constant.date_format'), strtotime($booking->created_at)),
                        $this->getStatusName($booking),
                        $booking->created_at->format('Y-m-d H:i:s')
                    ];
                }

                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get the status name for a booking (matching the exact logic from the table)
     *
     * @param \App\Booking $booking
     * @return string
     */
    private function getStatusName($booking)
    {
        $checkInDate = Carbon::parse($booking->check_in)->toDateString();
        $checkOutDate = Carbon::parse($booking->check_out)->toDateString();
        $today = now()->toDateString();
        $currentTime = now();
        $isOnGoing = false;
        $isCompleted = false;

        // Enhanced logic for determining ongoing status based on basis type
        if ($booking->status != 7 && $booking->status != 3) {
            if ($booking->listing_basis == 'Hourly') {
                // For hourly bookings: check slot times
                if ($booking->hourly_slots) {
                    $hasOngoingSlot = false;
                    $allSlotsCompleted = true;
                    $hasFutureSlot = false;
                    $appTimezone = config('app.timezone');

                    foreach ($booking->hourly_slots as $slot) {
                        // Parse slot time (format: "09:00 - 10:00")
                        $slotParts = explode(' - ', $slot->slot);
                        if (count($slotParts) == 2) {
                            $slotStartTime = Carbon::parse($slot->date . ' ' . $slotParts[0], $appTimezone);
                            $slotEndTime = Carbon::parse($slot->date . ' ' . $slotParts[1], $appTimezone);

                            // Handle midnight crossover (e.g., "23:00 - 01:00")
                            if ($slotEndTime <= $slotStartTime) {
                                $slotEndTime->addDay();
                            }

                            // Check if current time is within this slot (ongoing)
                            if ($currentTime->between($slotStartTime, $slotEndTime)) {
                                $hasOngoingSlot = true;
                                $allSlotsCompleted = false;
                                break;
                            }
                            // Check if slot is in the future
                            elseif ($slotStartTime > $currentTime) {
                                $hasFutureSlot = true;
                                $allSlotsCompleted = false;
                            }
                            // If slot end time is in the future, not all completed
                            elseif ($slotEndTime > $currentTime) {
                                $allSlotsCompleted = false;
                            }
                        }
                    }

                    if ($hasOngoingSlot) {
                        $isOnGoing = true;
                    } elseif ($allSlotsCompleted && !$hasFutureSlot) {
                        $isCompleted = true;
                    }
                }
            } else {
                // For daily bookings: check if today falls between check-in and check-out dates
                if ($checkInDate <= $today && $checkOutDate >= $today) {
                    $isOnGoing = true;
                }
            }
        }

        if ($booking->status == 7) {
            return 'Cancelled';
        } elseif ($isOnGoing) {
            return 'On Going';
        } elseif ($isCompleted || $booking->status == 3) {
            return 'Completed';
        } elseif ($booking->status == 0) {
            return 'Upcoming';
        } else {
            return 'Ongoing';
        }
    }

    function full_refund_booking($bookings)
    {
        try {
            return DB::transaction(function () use ($bookings) {
                foreach ($bookings as $booking) {
                    if ($booking->payment_method == 'card') {
                        $refund = $this->stripeService->refund($booking->charge_id, $booking->total_usd_amount);
                        if (isset($refund['status']) && $refund['status'] == false) {
                            return api_response(false, $refund['message']);
                        }
                    } elseif ($booking->payment_method == 'paypal') {
                        if (isset($refund['status']) && $refund['status'] == false) {
                            return api_response(false, $refund['message']);
                        }
                        $refund = $this->paypalService->refund($booking->charge_id, $booking->total_usd_amount);
                    }
                    $booking->customer->notify(new SuspendUserNotification($booking));
                    $booking->status = 7;
                    $booking->save();
                }
                return api_response(true, "Bookings Cancelled");
            });
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
}
