<div
    class="col-md-4 listing_card_parent @if ($list->status == '6') draft @elseif ($list->status == '1') accepted @elseif ($list->status == '0')@if (auth()->user()->hasRole('service')) in_review @endif @endif">
    <div class="card listing_card">
        <div class="card-header position-relative">
            <div class="card-img">
                <img src="{{ asset('website') . '/' . ($list->thumbnail_image->url ?? '') }}"
                    onerror="this.onerror=null;this.src=`{{ asset('website/images/favicon_white_bg.svg') }}`;"
                    alt="">
            </div>
            <div class="badge position-absolute status">
                @if ($list->status == 0)
                    <p class="m-0 status_name" data-status="in_review">
                        {{ translate('dashboard_listing.in_review') }}
                    </p>
                @elseif ($list->status == 1)
                    @if ($list->pause == 0)
                        <p class=" m-0 publish" data-status="accepted">
                            {{ translate('dashboard_listing.accepted') }} </p>
                    @else
                        <p class=" m-0 paused" data-status="paused">
                            {{ translate('dashboard_listing.paused') }} </p>
                    @endif
                @elseif ($list->status == 6)
                    <p class=" m-0 cancel" data-status="draft">
                        {{ translate('dashboard_listing.draft') }}
                    </p>
                @else
                    <p class=" m-0 cancel" data-status="{{ $list->statusName->name }}">
                        {{ $list->statusName->name }}
                    </p>
                @endif
            </div>
            <div class="dropdown position-absolute more_btn">
                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">
                    <i class="fa-solid fa-ellipsis"></i>
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    @if ($list->status == 1)
                        <a href="{{ url('/detail' . '/' . $list->ids . '/' . $list->slug) }}" class="dropdown-item"
                            target="_blank"
                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                            <i class="fas fa-eye" style="color: #000;"></i>
                              {{ translate('dashboard_listing.view_listing') }}
                        </a>
                        {{-- <a href="{{ url('/detail' . '/' . $item->id . '/' . $item->slug) }}"
                            class="dropdown-item" target="_blank"
                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                            {{ trans('view') }}
                        </a> --}}
                    @endif
                    @if ($list->status == 0)
                        @if (auth()->user()->hasRole(['user', 'sub_admin']))
                            <a href="{{ route('approve_listing', $list->id) }}" class="dropdown-item " title="">
                                <i class="fas fa-check-circle" style="color: green;"></i>
                                {{ translate('dashboard_listing.approve') }} {{ translate('dashboard_listing.listing') }}
                            </a>
                            <a href="" class="dropdown-item reject-btn" data-listing-id="{{ $list->id }}"
                                data-toggle="modal" data-target="#reject" title="">
                                <i class="fas fa-times-circle" style="color: red;"></i>
                                {{ translate('dashboard_listing.reject') }} {{ translate('dashboard_listing.listing') }}
                            </a>
                        @endif
                    @endif
                    {{-- edit --}}
                    @can('edit-' . str_slug('Listing'))
                        <a href="{{ url('listings') . "/{$list->category->slug}/$list->ids" }}" class="dropdown-item"
                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                            <i class="fas fa-pencil-alt" style="color: #000;"></i>
                             {{ translate('dashboard_listing.edit') }}  {{ translate('dashboard_listing.listing') }}
                        </a>
                    @endcan
                    {{-- duplicate --}}
                    @can('add-' . str_slug('Listing'))
                        @if ($list->status == 1)
                            <a href="{{ route('duplicate_listing', ["listing_ids" => $list->ids, "slug" => $list->category->slug]) }}" class="dropdown-item"
                                title="Duplicate {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                                <i class="fas fa-copy" style="color: #000;"></i>
                                {{ translate('dashboard_listing.duplicate') }} {{ translate('dashboard_listing.listing') }}
                            </a>
                        @endif
                    @endcan
                    {{-- @can('edit-' . str_slug('Listing')) --}}
                    @if ($list->status != 6)
                        @php
                            $internalName = $list->internal_name;
                        @endphp
                        <a href="" class="dropdown-item internal-name-model-btn" data-toggle="modal"
                            data-target="#internal_name" data-listing-id="{{ $list->ids }}"
                            data-internal-name="{{ $list->internal_name }}" title="Edit Internal Name">
                            <i class="fas fa-pencil-alt" style="color: #000;"></i>
                            @if ($internalName == '')
                                {{ translate('dashboard_listing.add') }}
                            @else
                                    {{ translate('dashboard_listing.edit') }}
                            @endif
                            {{ translate('dashboard_listing.internal_name') }}
                        </a>
                            @if ($list->status== 1)
                        <button class="dropdown-item copy-link" data-listing-id="{{ $list->ids }}"
                            {{-- data-link="{{ route('detail', ["locale" => app()->getLocale() ,'listing_id' => $list->ids ?? "", 'slug' => $list->slug ?? '']) }}" --}}
                            title="Copy Url"> 
                            <i class="fas fa-copy" style="color: #000;"></i>
                            {{ translate('dashboard_listing.copy_listing_url') }}
                        </button>
                        @endif
                        {{-- Pause/UnPause  --}}
                        @if ($list->statusName->name != 'In Review')
                            @if ($list->pause == 1)
                                <button class="dropdown-item btn pause_btn" data-listing-id="{{ $list->ids }}"
                                    data-pause-type="unpause">
                                    <i class="fas fa-pause" style="color: #000;"></i>
                                    {{ translate('dashboard_listing.unpause_listing') }}
                                </button>
                            @else
                                <button class="dropdown-item btn pause_btn" data-listing-id="{{ $list->ids }}"
                                    data-pause-type="pause">
                                    <i class="fas fa-pause" style="color: #000;"></i>
                                    {{ translate('dashboard_listing.pause_listing') }}
                                </button>
                            @endif
                            {{-- Pause/UnPause end --}}
                        @endif
                    @endif
                    {{-- @endcan --}}
                    @if (count($list->files) > 0)
                        <a href="" class="dropdown-item get-listing-document" data-toggle="modal"
                            data-target="#uploaded_doc" data-listing-id="{{ $list->ids }}"
                            title="View Document {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                            <i class="fas fa-file" style="color: #000;"></i>
                            {{ translate('dashboard_listing.view_documents') }}
                        </a>
                    @endif

                    {{-- @can('view-' . str_slug('Listing'))
                        <a class="dropdown-item"
                            href="{{ route('listing.show', $item->ids) }}">{{ trans('Show') }}</a>
                    @endcan --}}

                    @if (auth()->user()->hasRole('user'))
                        {{-- Pause/UnPause  --}}
                        @if ($list->statusName->name != 'Pending')
                            @if ($list->pause == 1)
                                <button class="dropdown-item btn pause_btn" data-listing-id="{{ $list->ids }}"
                                    data-pause-type="unpause">
                                    <i class="fas fa-pause" style="color: #000;"></i>
                                    {{ translate('dashboard_listing.unpause_listing') }}
                                </button>
                            @else
                                <button class="dropdown-item btn pause_btn" data-listing-id="{{ $list->ids }}"
                                    data-pause-type="pause">
                                    <i class="fas fa-pause" style="color: #000;"></i>
                                    {{ translate('dashboard_listing.pause_listing') }}
                                </button>
                            @endif
                        @endif
                        {{-- Pause/UnPause end --}}

                        @if ($list->status == 5)
                            <button class="dropdown-item unsuspend_btn" data-listing-id="{{ $list->ids }}">
                                <i class="fas fa-pause" style="color: #000;"></i>
                                  {{ translate('dashboard_listing.unsuspend') }}
                            </button>
                            {{-- <a class="dropdown-item  suspend_btn"
                                data-type="unsuspend"
                                data-listing-id="{{ $item->id }}">{{ trans('unsuspend') }}</a> --}}
                        @elseif ($list->status == 1)
                            <button class="dropdown-item suspend_btn text-danger" data-toggle="modal"
                                data-target="#suspend" data-listing-id="{{ $list->ids }}">
                                <i class="fas fa-pause" style="color: #000;"></i>
                                 {{ translate('dashboard_listing.suspend') }}
                            </button>
                            {{-- <a class="dropdown-item text-danger suspend_btn"
                                data-type="suspend"
                                data-listing-id="{{ $item->id }}">{{ trans('suspend') }}</a> --}}
                        @endif
                    @endif
                    {{-- delete --}}
                    @can('delete-' . str_slug('Listing'))
                        <button type="submit" class="dropdown-item text-danger del-active-listing bold"
                            data-listing-id="{{ $list->ids }}" data-toggle="modal" data-target="#delete_listing"
                            title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Listing') }}">
                            <i class="fas fa-trash" style="color: red;"></i>
                          {{ translate('dashboard_listing.delete_listing') }}
                        </button>
                    @endcan
                </div>
            </div>
        </div>
        <div class="card-body">
            <h4 class="title m-0">{{ $list->name }}</h4>
            <p class="text-black-50 internal-name">
                {{ $list->internal_name }}
            </p>
            <p class="text-danger days-left">
                @if ($list->days_left < 5 && $list->days_left > 0)
                    <span class="text-danger">{{ $list->days_left }} {{ translate('dashboard_listing.days_left') }}</span>
                @elseif ($list->days_left > 0)
                    {{ $list->days_left }} {{ translate('dashboard_listing.days_left') }}
                @else
                    <span class="text-danger">{{ translate('dashboard_listing.deleted') }}</span>
                @endif
            </p>
            <div class="d-flex gap-2 align-items-center listing_info">
                <img src="{{ asset('website') . '/' . $list->category->image }}" alt="" height="14px"
                    width="14px">
                <p class="m-0">
                    {{ $list->category->display_name ?? '-' }}
                </p>
            </div>
            <div class="d-flex gap-2 align-items-center listing_info">
                <i class="fas fa-calendar-alt fs-16"></i>
                <p class="m-0">
                    @php
                        $booking = count($list->active_bookings);
                    @endphp
                    @if ($booking == 0)
                        {{ translate('dashboard_listing.no_active_bookings') }}
                    @else
                        {{ $booking }} {{ translate('dashboard_listing.active_bookings') }}
                    @endif
                </p>
            </div>
            <div class="d-flex gap-2 align-items-center listing_info">
                <i class="fas fa-star fs-13"></i>
                <p class="m-0">
                    @php
                        $rating = $list->rating == '' ? '0.0' : $list->rating;
                    @endphp
                    @if ($rating == 0)
                        {{ translate('dashboard_listing.new_listing') }}
                    @else
                        {{ $rating }}
                    @endif
                </p>
            </div>
        </div>
    </div>
</div>
