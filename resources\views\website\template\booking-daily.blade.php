<div class="row py-3 g-0 book ps-0">
    <h3 class="fs-22 listing_data_heading pb-sm-4 pb-1">{{ translate('listing_details.book_your') }} {{ \Illuminate\Support\Str::singular($category->display_name ?? '-') }} {{ translate('listing_details.now') }}</h3>
    <form id="booking-form" class="d-flex gap-10 ps-0">
        <input type="hidden" name="listing_id" value="{{ $listing->ids }}">
        {{-- calculation div --}}
        <div class="col-xl-5 col-lg-6 col-md-12" data-aos="fade-right">
            <div class="info px-4 pt-sm-4 pb-sm-5 py-3 px-4 booking_info select_date" id="calculation_detail">
                {{ translate('listing_details.please_select_the_date') }}
            </div>
        </div>
        {{-- calculation div end --}}

        <div class="col-lg-5 col-md-12" data-aos="fade-left">
            <div class="head ps-lg-4 ps-md-0">
                <h3 class="fs-20 light-bold pb-3">
                    <span class="total_days">0 @if ($category->id == 4)
                            {{ translate('listing_details.night_at') }}
                        @elseif ($category->id == 2)
                            {{ translate('listing_details.day_on') }}
                        @else
                            {{ translate('listing_details.day_with') }}
                        @endif
                    </span>
                    {{ translateDynamic($listing->name,app()->getLocale(),$listing->locale) ?? '-' }}
                </h3>
                <p class="fs-14">
                    <span class="check_in">- -</span>
                    <span class="check-cont">
                        {{ translate('listing_details.to') }} <span class="check_out">- -</span>
                    </span>
                </p>
            </div>
            <div class="cal">
                <div class="calendar"></div>
            </div>
        </div>
    </form>
</div>

@php
    $restricted_days =
        $listing->detail->placa_restriction == 'yes' ? $listing->restricted_days->pluck('day')->toArray() ?? [] : [];
@endphp
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pg-calendar@1.4.31/dist/js/pignose.calendar.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.40/moment-timezone-with-data.min.js"></script>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>


    <script>
        $(document).ready(function() {
            const policyType = @js($listing->detail->cancellation_policy);
            // var currentDate = moment(new Date()).format('YYYY-MM-DD');
            var currentDate = moment().tz("America/Bogota").format('YYYY-MM-DD');
            const date_format = "Do MMM YYYY";
            let loader = `<div id="listing-loading"><div class="loader"></div></div>`;
            const restricted_days = @js($restricted_days ?? []);

            function cancellation_policy(startDate) {
                $.ajax({
                    type: 'GET',
                    // url: `{{ url('cancellation-policy-timeline') }}/` + id + `/` + category,
                    url: `{{ url('cancellation-policy-timeline') }}/` + policyType +
                        `/` +
                        startDate,
                    data: {
                        policyType: policyType,
                        startDate: startDate
                    },
                    success: function(data) {
                        if (data) {
                            $('.main_col_listing_cancellation').show();
                            $("#cancellation_data").html(data);
                            console.log(policyType);
                        } else {
                            console.log('Error: No view received.');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        Swal.fire(
                            `{{ translate('listing_details.error') }}`,
                            `{{ translate('listing_details.please_select_a_valid_view') }}`,
                            'error'
                        )
                    }
                });
            }

            @if (isset($enable_dates[0]))
                cancellation_policy(@json($enable_dates[0]));
            @endif

            const calculcation_detail = (start_date, end_date = null) => {
                let guest = $("#guest").val();
                $("#calculation_detail").html(loader);
                $.ajax({
                    type: 'GET',
                    url: `{{ route('calculate_detail_daily') }}`,
                    data: {
                        start_date: start_date,
                        end_date: end_date,
                        guest: guest,
                        listing_id: '{{ $listing->ids }}',
                        basis_type: '{{ $listing->detail->basis_type }}',
                    },
                    success: function(response) {
                        if (response.status) {
                            $("#calculation_detail").html(response.data).removeClass('select_date');
                        } else {
                            console.log('Error: No view received.');
                            $("#calculation_detail").html('Select a valid date').addClass(
                                'select_date');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        Swal.fire(
                            `{{ translate('listing_details.error') }}`,
                            `{{ translate('listing_details.please_select_a_valid_view') }}`,
                            'error'
                        )
                    },
                    complete: function() {
                        $('#check_in_time, #check_out_time').flatpickr({
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "h:i K",
                            time_24hr: false,
                            allowInput: true,
                        });
                    }
                });
            }
            
            $('input.time_picker').timepicker({
                interval: 60,
            });

            function booking_submit(url, form_data) {
                $.ajax({
                    url: `${url}`,
                    data: form_data,
                    type: 'POST',
                    success: function(response) {
                        console.log(response);
                        if (response.status === true) {
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     position: 'top-right',
                            //     showHideTransition: 'slide',
                            //     icon: 'success'
                            // })
                            if (response.url) {
                                window.location.href = response.url
                            }
                        } else {
                            Swal.fire(
                                `{{ translate('listing_details.oops') }}`,
                                `${response.message}`,
                                'error'
                            )
                            if (response.url) {
                                window.location.href = response.url
                            }
                        }
                    }
                });
            }
            $(document).on("click", "#reserve_btn", function() {
                let reserve_data = $("#booking-form").serialize();
                booking_submit('{{ route('reserve_data') }}', reserve_data);
            })


            var minDays = @js($listing->detail->minimum_stay_length ?? null); // Minimum selection days
            var selectedDates = [];

            function hasRestrictedDays(start, end) {
                if (!start) return false;
                let currentDate = moment(start);
                const endMoment = end ? moment(end) : moment(start);
                while (currentDate.isSameOrBefore(endMoment)) {
                    const dayName = currentDate.format('dddd');
                    if (restricted_days.includes(dayName)) {
                        return true;
                    }
                    currentDate.add(1, 'days');
                }
                return false;
            }

            // var currentDate = moment().tz("America/Bogota").format('YYYY-MM-DD');
    //         var testing = false;
    //         $('.calendar').pignoseCalendar({
    //             lang: "{{ app()->getLocale() }}",
    //             multiple: true,
    //             minDate: currentDate,
    //             initialize: false,
    //             disabledDates: @json($reserve_dates_array),
    //             enabledDates: @json($enable_dates),
    //             select: function(dates, context) {
    //                 var start = dates[0];
    //                 var end = dates[1];
    //                 var startDate = start ? moment(start).format('YYYY-MM-DD') : '';
    //                 var endDate = end ? moment(end).format('YYYY-MM-DD') : '';
    //                 var totalDays = start && end ? moment(end).diff(moment(start), 'days') : 0;

    //                 // Function to check if any restricted days are included

    //                 if (start) {
    //                     cancellation_policy(startDate);
    //                     // Check for restricted days and show warning if needed
    //                     if (hasRestrictedDays(startDate, endDate)) {
    //                         if (testing) {
    //                             Swal.fire({
    //                                 icon: 'warning',
    //                                 title: 'Pico y Placa Applies',
    //                                 text: `{{ translate('listing_details.pico_y_placa_restriction_warning') }}`,
    //                             });
    //                         }
    //                         testing = true;
    //                         // if (start && end) { // Only reset for range selection
    //                         //     $('.calendar').pignoseCalendar('set', selectedDates);
    //                         //     return;
    //                         // }
    //                     }

    //                     if (start && end) {
    //                         // Check minimum days requirement
    //                         // if (minDays && totalDays < minDays) {
    //                         //     Swal.fire({
    //                         //         icon: 'warning',
    //                         //         title: 'Minimum Selection Required',
    //                         //         text: `You must select at least ${minDays} days.`,
    //                         //     });
    //                         //     $('.calendar').pignoseCalendar('set', selectedDates);
    //                         //     return;
    //                         // }
    //                         // Update UI range selection
    //                         $(".check_in").html(moment(startDate).format(date_format));
    //                         $(".check_out").html(moment(endDate).format(date_format));
    //                         $(".check-cont").show();
    //                         $("#reserve_btn").prop('disabled', false);
    //                         selectedDates = dates;
    //                         // Update UI range selection end
    //                     } else {
    //                         // Single date selection
    //                         totalDays = 1;
    //                         $(".check_in").html(moment(startDate).format(date_format));
    //                         $(".check_out").html("Select a date");
    //                         $("#reserve_btn").prop('disabled', true);
    //                         $(".check-cont").hide();
    //                         // Single date selection end

    //                     }
    //                 } else {
    //                     // No dates selected
    //                     $("#reserve_btn").prop('disabled', true);
    //                     $(".check_in").html("Select a date");
    //                     $(".check_out").html("Select a date");
    //                     $(".check-cont").hide();
    //                     // No dates selected end

    //                 }

    // var night_at = `{{ translate('listing_details.night_at') }}`;
    // var nights_at = `{{ translate('listing_details.nights_at') }}`;
    // var day_on = `{{ translate('listing_details.day_on') }}`;
    // var days_on = `{{ translate('listing_details.days_on') }}`;
    // var day_with = `{{ translate('listing_details.day_with') }}`;
    // var days_with = `{{ translate('listing_details.days_with') }}`;

    // @if ($listing->category_id == 4)
    //     $(".total_days").html(totalDays <= 1 ? `${totalDays} ${night_at}` : `${totalDays} ${nights_at}`);
    // @elseif ($listing->category_id == 2)
    //     $(".total_days").html(totalDays <= 1 ? `${totalDays} ${day_on}` : `${totalDays} ${days_on}`);
    // @else
    //     $(".total_days").html(totalDays <= 1 ? `${totalDays} ${day_with}` : `${totalDays} ${days_with}`);
    // @endif
    // calculcation_detail(startDate, endDate);
    //             }
    //         });



    // CODE FOR SELECTING THE NEAREST DATE FROM TODAY'S DATE STARTS FROM HERE



            var currentDate = new Date();
            var reserved = @json($reserve_dates_array) || [];
            var enabled  = @json($enable_dates) || [];
            var hasWhitelist = enabled.length > 0;

            function getFirstAvailableDate(startDate) {
                var maxSearchDays = 365;
                for (var i = 0; i < maxSearchDays; i++) {
                    var d = moment(startDate).add(i, 'days').format('YYYY-MM-DD');
                    var isReserved = reserved.includes(d);
                    var isEnabled = !hasWhitelist || enabled.includes(d);

                    if (!isReserved && isEnabled) {
                        return d;
                    }
                }
                return null;
            }

            var testing = false;
            $('.calendar').pignoseCalendar({
                lang: "{{ app()->getLocale() }}",
                multiple: true,
                minDate: currentDate,
                initialize: false,
                disabledDates: reserved,
                enabledDates: enabled,
                select: function(dates, context) {
                    var start = dates[0];
                    var end = dates[1];
                    var startDate = start ? moment(start).format('YYYY-MM-DD') : '';
                    var endDate = end ? moment(end).format('YYYY-MM-DD') : '';
                    var totalDays = start && end ? moment(end).diff(moment(start), 'days') : 0;

                    if (start) {
                        cancellation_policy(startDate);

                        if (hasRestrictedDays(startDate, endDate)) {
                            if (testing) {
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Pico y Placa Applies',
                                    text: `{{ translate('listing_details.pico_y_placa_restriction_warning') }}`,
                                });
                            }
                            testing = true;
                        }

                        if (start && end) {
                            $(".check_in").html(moment(startDate).format(date_format));
                            $(".check_out").html(moment(endDate).format(date_format));
                            $(".check-cont").show();
                            $("#reserve_btn").prop('disabled', false);
                            selectedDates = dates;
                        } else {
                            totalDays = 1;
                            $(".check_in").html(moment(startDate).format(date_format));
                            $(".check_out").html("Select a date");
                            $("#reserve_btn").prop('disabled', true);
                            $(".check-cont").hide();
                        }
                    } else {
                        $("#reserve_btn").prop('disabled', true);
                        $(".check_in").html("Select a date");
                        $(".check_out").html("Select a date");
                        $(".check-cont").hide();
                    }

                    var night_at = `{{ translate('listing_details.night_at') }}`;
                    var nights_at = `{{ translate('listing_details.nights_at') }}`;
                    var day_on = `{{ translate('listing_details.day_on') }}`;
                    var days_on = `{{ translate('listing_details.days_on') }}`;
                    var day_with = `{{ translate('listing_details.day_with') }}`;
                    var days_with = `{{ translate('listing_details.days_with') }}`;

                    @if ($listing->category_id == 4)
                        $(".total_days").html(totalDays <= 1 ? `${totalDays} ${night_at}` : `${totalDays} ${nights_at}`);
                    @elseif ($listing->category_id == 2)
                        $(".total_days").html(totalDays <= 1 ? `${totalDays} ${day_on}` : `${totalDays} ${days_on}`);
                    @else
                        $(".total_days").html(totalDays <= 1 ? `${totalDays} ${day_with}` : `${totalDays} ${days_with}`);
                    @endif

                    calculcation_detail(startDate, endDate);
                }
            });

            // var firstAvailable = getFirstAvailableDate(currentDate);
            // if (firstAvailable) {
            //     $('.calendar').pignoseCalendar('set', moment(firstAvailable).toDate());
            // }


            var firstAvailable = getFirstAvailableDate(currentDate);
            if (firstAvailable && !hasRestrictedDays(firstAvailable, firstAvailable)) {
                // Focus calendar to correct month/day (no auto-select yet)
                $('.calendar').pignoseCalendar('set', null, {
                    date: moment(firstAvailable).toDate()
                });

                // When the user first hovers/clicks, THEN auto-select
                $('.calendar').one('mouseenter click', function () {
                    $('.calendar').pignoseCalendar('set', moment(firstAvailable).toDate());
                });
            }




    // CODE FOR SELECTING THE NEAREST DATE FROM TODAY'S DATE ENDS HERE



            // Tooltip for invalid selections
            $(document).on('mouseenter', '.pignose-calendar-unit:not(.pignose-calendar-unit-disabled)', function(
                e) {
                var selectedArray = $('.pignose-calendar-unit.pignose-calendar-unit-active').map(
                    function() {
                        return $(this).attr(
                            'data-date'); // Ensure your date elements have a data-date attribute
                    }).get();

                var firstSelectedDate = selectedArray.length > 0 ? moment(selectedArray[0]) : null;
                var hoveredDate = $(this);
                var hoveredDateValue = hoveredDate.attr('data-date') ? moment(hoveredDate.attr(
                    'data-date')) : null;

                var currentDate = moment().format('YYYY-MM-DD'); // Get the formatted current date

                if (firstSelectedDate && hoveredDateValue) {
                    // Prevent tooltip from showing for past dates
                    if (hoveredDateValue.isBefore(currentDate, 'day')) {
                        return; // Exit early to avoid showing tooltip
                    }

                    // Calculate the new total days if this date is selected
                    var newTotalDays = hoveredDateValue.diff(firstSelectedDate, 'days');

                    // Show tooltip only if selection would be less than the minimum required days
                    if (newTotalDays < minDays && minDays != null && minDays != 1) {
                        tippy(this, {
                            content: `${minDays} nights minimum.`,
                            placement: 'top',
                            arrow: true,
                            theme: 'light',
                            delay: [0, 100],
                        }).show();
                    }
                }
            });

        });
    </script>
    <script>
        $(document).ready(function() {

        })
    </script>
@endpush
