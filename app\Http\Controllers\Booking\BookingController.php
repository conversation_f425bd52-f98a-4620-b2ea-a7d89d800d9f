<?php

namespace App\Http\Controllers\Booking;

use App\Http\Controllers\Controller;
use App\Booking;
use App\Services\BookingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{
    protected $bookingService;

    public function __construct(BookingService $bookingService)
    {
        $this->middleware('auth');
        $this->bookingService = $bookingService;
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        // Check user permission
        $model = str_slug('booking', '-');
        $permission = auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first();
        if (!$permission) {
            return response(view('403'), 403);
        }

        // Get bookings using the service
        $booking = $this->bookingService->getBookingsWithFilters($request);
        $status = $request->get('status');

        // Get today and tomorrow bookings for dashboard tabs
        $today_bookings = $this->getTodayBookings();
        $tomorrow_bookings = $this->getTomorrowBookings();

        // Return appropriate response
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'html' => view('booking.booking.partials.booking_table', compact('booking', 'status'))->render(),
                'pagination' => $booking->appends($request->except('page'))->render(),
                'status' => $status,
                'today_count' => $today_bookings->count(),
                'tomorrow_count' => $tomorrow_bookings->count()
            ]);
        }

        return view('booking.booking.index', compact('booking', 'status', 'today_bookings', 'tomorrow_bookings'));
    }

    /**
     * Get today's bookings - same condition as in DashboardController
     */
    private function getTodayBookings()
    {
        $today = \Carbon\Carbon::today()->toDateString();

        $query = Booking::where("status", 0)
            ->where(function ($q) use ($today) {
                // Include bookings that are ongoing today (check_in <= today AND check_out >= today)
                $q->where(DB::raw('DATE(check_in)'), '<=', $today)
                    ->where(DB::raw('DATE(check_out)'), '>=', $today);
            })
            ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots']);

        // Apply user role filtering
        if (auth()->user()->hasRole('service')) {
            $query->where("provider_id", auth()->id());
        } elseif (!auth()->user()->hasRole(['user', 'sub_admin'])) {
            $query->where("user_id", auth()->id());
        }

        return $query->get();
    }

    /**
     * Get tomorrow's bookings - check next day check_in date
     */
    private function getTomorrowBookings()
    {
        $today = \Carbon\Carbon::today()->toDateString();
        $tomorrow = \Carbon\Carbon::tomorrow()->toDateString();

        $query = Booking::where("status", 0)
            ->where(DB::raw('DATE(check_in)'), '=', $tomorrow)
            ->where(DB::raw('DATE(check_in)'), '>', $today) // Explicitly exclude today
            ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots']);

        // Apply user role filtering
        if (auth()->user()->hasRole('service')) {
            $query->where("provider_id", auth()->id());
        } elseif (!auth()->user()->hasRole(['user', 'sub_admin'])) {
            $query->where("user_id", auth()->id());
        }

        return $query->get();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            return view('booking.booking.create');
        }
        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            $this->validate($request, [
                'user_id' => 'required',
                'listing_id' => 'required'
            ]);
            $requestData = $request->all();

            Booking::create($requestData);
            return redirect('booking/booking')->with('flash_message', 'Booking added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first() != null) {
            $booking = Booking::findOrFail($id);
            return view('booking.booking.show', compact('booking'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $booking = Booking::findOrFail($id);
            return view('booking.booking.edit', compact('booking'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $this->validate($request, [
                'user_id' => 'required',
                'listing_id' => 'required'
            ]);
            $requestData = $request->all();

            $booking = Booking::findOrFail($id);
            $booking->update($requestData);

            return redirect('booking/booking')->with('flash_message', 'Booking updated!');
        }
        return response(view('403'), 403);
    }

    /**
     * Export bookings to CSV file based on status
     *
     * @param \Illuminate\Http\Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportCsv(Request $request)
    {
        $model = str_slug('booking', '-');
        $permission = auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first();
        if (!$permission) {
            return response(view('403'), 403);
        }

        return $this->bookingService->exportBookingsCsv($request);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'delete-' . $model)->first() != null) {
            Booking::destroy($id);
            return redirect('booking/booking')->with('flash_message', 'Booking deleted!');
        }
        return response(view('403'), 403);
    }
}
